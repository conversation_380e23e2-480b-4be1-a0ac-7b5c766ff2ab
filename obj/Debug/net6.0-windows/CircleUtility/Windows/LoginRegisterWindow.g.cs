#pragma checksum "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6E7EFF2F40A6B3B668578BF317982DC276DBEF77"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Windows {
    
    
    /// <summary>
    /// LoginRegisterWindow
    /// </summary>
    public partial class LoginRegisterWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 251 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoginPanel;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoginTitle;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LoginUsernameBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox LoginPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LoginPasswordBoxVisible;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoginShowPasswordButton;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberMeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoginErrorMessage;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoginButton;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoginStatusText;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid RegisterPanel;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RegisterTitle;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RegisterUsernameBox;
        
        #line default
        #line hidden
        
        
        #line 357 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox RegisterPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RegisterPasswordBoxVisible;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RegisterShowPasswordButton;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox RegisterConfirmPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 387 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RegisterConfirmPasswordBoxVisible;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RegisterConfirmShowPasswordButton;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RegisterErrorMessage;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RegisterButton;
        
        #line default
        #line hidden
        
        
        #line 423 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RegisterStatusText;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 433 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTitle;
        
        #line default
        #line hidden
        
        
        #line 450 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse LoadingSpinner;
        
        #line default
        #line hidden
        
        
        #line 458 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingStatusText;
        
        #line default
        #line hidden
        
        
        #line 477 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar LoadingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 486 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressPercentage;
        
        #line default
        #line hidden
        
        
        #line 498 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingDetailsText;
        
        #line default
        #line hidden
        
        
        #line 511 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingOverlay;
        
        #line default
        #line hidden
        
        
        #line 524 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse LoadingSpinnerSmall;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TheCircleUtility;component/circleutility/windows/loginregisterwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 18 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            ((CircleUtility.Windows.LoginRegisterWindow)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Window_MouseDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 254 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 258 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.LoginPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.LoginTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.LoginUsernameBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 279 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.LoginUsernameBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 7:
            this.LoginPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 287 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.LoginPasswordBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 8:
            this.LoginPasswordBoxVisible = ((System.Windows.Controls.TextBox)(target));
            
            #line 290 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.LoginPasswordBoxVisible.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 9:
            this.LoginShowPasswordButton = ((System.Windows.Controls.Button)(target));
            
            #line 304 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.LoginShowPasswordButton.Click += new System.Windows.RoutedEventHandler(this.ShowPasswordButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RememberMeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.LoginErrorMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.LoginButton = ((System.Windows.Controls.Button)(target));
            
            #line 321 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.LoginButton.Click += new System.Windows.RoutedEventHandler(this.LoginButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 326 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.RegisterLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 14:
            this.LoginStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.RegisterPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.RegisterTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.RegisterUsernameBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 350 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.RegisterUsernameBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 18:
            this.RegisterPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 359 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.RegisterPasswordBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 19:
            this.RegisterPasswordBoxVisible = ((System.Windows.Controls.TextBox)(target));
            
            #line 362 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.RegisterPasswordBoxVisible.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 20:
            this.RegisterShowPasswordButton = ((System.Windows.Controls.Button)(target));
            
            #line 376 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.RegisterShowPasswordButton.Click += new System.Windows.RoutedEventHandler(this.ShowPasswordButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.RegisterConfirmPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 386 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.RegisterConfirmPasswordBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 22:
            this.RegisterConfirmPasswordBoxVisible = ((System.Windows.Controls.TextBox)(target));
            
            #line 389 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.RegisterConfirmPasswordBoxVisible.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 23:
            this.RegisterConfirmShowPasswordButton = ((System.Windows.Controls.Button)(target));
            
            #line 403 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.RegisterConfirmShowPasswordButton.Click += new System.Windows.RoutedEventHandler(this.ShowPasswordButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.RegisterErrorMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.RegisterButton = ((System.Windows.Controls.Button)(target));
            
            #line 415 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            this.RegisterButton.Click += new System.Windows.RoutedEventHandler(this.RegisterButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 420 "..\..\..\..\..\CircleUtility\Windows\LoginRegisterWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.LoginLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 27:
            this.RegisterStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.LoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 29:
            this.LoadingTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.LoadingSpinner = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 31:
            this.LoadingStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.LoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 33:
            this.ProgressPercentage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.LoadingDetailsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.LoadingOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 36:
            this.LoadingSpinnerSmall = ((System.Windows.Shapes.Ellipse)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

