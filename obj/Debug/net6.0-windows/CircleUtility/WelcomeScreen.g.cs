#pragma checksum "..\..\..\..\CircleUtility\WelcomeScreen.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0DF8934F5CE54115404A15AF8F357712E3287678"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility {
    
    
    /// <summary>
    /// WelcomeScreen
    /// </summary>
    public partial class WelcomeScreen : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 170 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleShadow;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusMessage;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameInput;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordInput;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingBar;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingStatus;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AccessButton;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TheCircleUtility;component/circleutility/welcomescreen.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleShadow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.StatusMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.UsernameInput = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.PasswordInput = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 6:
            this.LoadingBar = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.LoadingStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.AccessButton = ((System.Windows.Controls.Button)(target));
            return;
            case 9:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 294 "..\..\..\..\CircleUtility\WelcomeScreen.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

