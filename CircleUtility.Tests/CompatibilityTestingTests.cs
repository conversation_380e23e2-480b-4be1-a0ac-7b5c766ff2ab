// Created by Arsenal on 5-17-25 12:15PM
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CircleUtility.Models;
using CircleUtility.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace CircleUtility.Tests
{
    [TestClass]
    public class CompatibilityTestingTests
    {
        private Mock<IHardwareDetectionService> _mockHardwareDetectionService = null!;
        private Mock<IHardwareOptimizationService> _mockOptimizationService = null!;
        private Mock<IPerformanceMonitoringService> _mockPerformanceMonitoringService = null!;
        private Mock<IBenchmarkingService> _mockBenchmarkingService = null!;
        private CompatibilityTestingService _testingService = null!;
        private HardwareSpecificOptimization _testOptimization = null!;
        private PowerManagementProfile _testProfile = null!;

        [TestInitialize]
        public void TestInitialize()
        {
            // Create mock services
            _mockHardwareDetectionService = new Mock<IHardwareDetectionService>();
            _mockOptimizationService = new Mock<IHardwareOptimizationService>();
            _mockPerformanceMonitoringService = new Mock<IPerformanceMonitoringService>();
            _mockBenchmarkingService = new Mock<IBenchmarkingService>();

            // Create test optimization
            _testOptimization = new HardwareSpecificOptimization
            {
                Name = "Test Optimization",
                Description = "Test optimization for testing",
                Category = "Test Category",
                HardwareType = HardwareType.GPU,
                Manufacturer = "NVIDIA",
                ModelPattern = "RTX.*",
                Method = OptimizationMethod.Registry,
                RegistryKey = "HKEY_LOCAL_MACHINE\\Test",
                RegistryValueName = "TestValue",
                RegistryValue = 1,
                RegistryValueKind = "DWord",
                PerformanceImpact = 80,
                StabilityImpact = 90,
                PowerConsumptionImpact = 70,
                ThermalImpact = 60,
                InputLatencyImpact = 85,
                Risk = RiskLevel.Low
            };

            // Create test power profile
            _testProfile = new PowerManagementProfile
            {
                Name = "Test Profile",
                Description = "Test power profile for testing",
                Category = "Test Category",
                CpuSettings = new CpuPowerSettings
                {
                    PowerPlan = "High Performance",
                    MinProcessorState = 50,
                    MaxProcessorState = 100
                },
                GpuSettings = new GpuPowerSettings
                {
                    PowerManagementMode = "Prefer Maximum Performance",
                    PowerLimit = 100
                },
                PerformanceImpact = 85,
                PowerEfficiency = 40,
                ThermalImpact = 75,
                NoiseLevel = 70,
                InputLatencyImpact = 90,
                Risk = RiskLevel.Low
            };

            // Setup mock benchmarking service with a base score
            var baseResult = new CircleUtility.Services.BenchmarkPerformanceResult
            {
                BenchmarkType = BenchmarkType.Quick,
                Score = 1000, // Base score
                Duration = TimeSpan.FromSeconds(5),
                AverageFps = 100,
                MinimumFps = 90,
                MaximumFps = 110,
                FrameTimeAvg = 10,
                FrameTime99Percentile = 15,
                IsSuccessful = true
            };

            _mockBenchmarkingService.Setup(m => m.RunBenchmarkAsync(It.IsAny<BenchmarkType>()))
                .ReturnsAsync(baseResult);

            _mockBenchmarkingService.Setup(m => m.RunBenchmarkAsync(It.IsAny<BenchmarkType>(), It.IsAny<System.Threading.CancellationToken>()))
                .ReturnsAsync(baseResult);

            // Setup mock optimization service
            _mockOptimizationService.Setup(m => m.ApplyOptimizationAsync(It.IsAny<HardwareSpecificOptimization>(), It.IsAny<bool>()))
                .ReturnsAsync(true);

            _mockOptimizationService.Setup(m => m.RevertOptimizationAsync(It.IsAny<HardwareSpecificOptimization>()))
                .ReturnsAsync(true);

            // Setup mock performance monitoring service
            _mockPerformanceMonitoringService.Setup(m => m.GetCurrentMetrics())
                .Returns(new PerformanceMetrics
                {
                    Timestamp = DateTime.Now,
                    CpuUsage = 50,
                    GpuUsage = 60,
                    CpuTemperature = 70,
                    GpuTemperature = 75,
                    RamUsage = 8,
                    FPS = 100,
                    FrameTime = 10
                });

            // Create testing service with mocked dependencies
            _testingService = new CompatibilityTestingService(
                _mockHardwareDetectionService.Object,
                _mockOptimizationService.Object,
                _mockPerformanceMonitoringService.Object,
                _mockBenchmarkingService.Object);
        }

        [TestMethod]
        public async Task TestOptimizationAsync_ShouldReturnSuccessfulResult()
        {
            // Arrange - already done in TestInitialize

            // Setup optimized benchmark to return higher score
            var improvedResult = new CircleUtility.Services.BenchmarkPerformanceResult
            {
                BenchmarkType = BenchmarkType.Quick,
                Score = 1100, // 10% improvement
                Duration = TimeSpan.FromSeconds(5),
                AverageFps = 110,
                MinimumFps = 100,
                MaximumFps = 120,
                FrameTimeAvg = 9,
                FrameTime99Percentile = 14,
                IsSuccessful = true
            };

            // First call returns base score, second call returns improved score
            int callCount = 0;
            _mockBenchmarkingService.Setup(m => m.RunBenchmarkAsync(It.IsAny<BenchmarkType>()))
                .ReturnsAsync(() => {
                    callCount++;
                    return callCount == 1 ?
                        new CircleUtility.Services.BenchmarkPerformanceResult
                        {
                            BenchmarkType = BenchmarkType.Quick,
                            Score = 1000,
                            IsSuccessful = true
                        } : improvedResult;
                });

            _mockBenchmarkingService.Setup(m => m.RunBenchmarkAsync(It.IsAny<BenchmarkType>(), It.IsAny<System.Threading.CancellationToken>()))
                .ReturnsAsync(() => {
                    return callCount == 1 ?
                        new CircleUtility.Services.BenchmarkPerformanceResult
                        {
                            BenchmarkType = BenchmarkType.Quick,
                            Score = 1000,
                            IsSuccessful = true
                        } : improvedResult;
                });

            // Act
            var result = await _testingService.TestOptimizationAsync(_testOptimization, 5); // Short test duration for unit test

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsSuccessful);
            Assert.AreEqual(_testOptimization.Name, result.OptimizationName);
            Assert.IsTrue(result.PerformanceImpact > 0);

            // Verify that optimization was applied and then reverted
            _mockOptimizationService.Verify(m => m.ApplyOptimizationAsync(_testOptimization, true), Times.Once);
            _mockOptimizationService.Verify(m => m.RevertOptimizationAsync(_testOptimization), Times.Once);
        }

        [TestMethod]
        public async Task TestOptimizationAsync_WithFailedApply_ShouldReturnFailedResult()
        {
            // Arrange
            _mockOptimizationService.Setup(m => m.ApplyOptimizationAsync(It.IsAny<HardwareSpecificOptimization>(), It.IsAny<bool>()))
                .ReturnsAsync(false);

            // Act
            var result = await _testingService.TestOptimizationAsync(_testOptimization, 5);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsSuccessful);
            Assert.AreEqual(_testOptimization.Name, result.OptimizationName);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage.Contains("Failed to apply"));

            // Verify that revert was not called
            _mockOptimizationService.Verify(m => m.RevertOptimizationAsync(_testOptimization), Times.Never);
        }

        [TestMethod]
        public async Task TestOptimizationAsync_WithStressTestFailure_ShouldReturnFailedResult()
        {
            // Arrange
            // Setup performance monitoring to simulate thermal throttling
            var highTempMetrics = new PerformanceMetrics
            {
                Timestamp = DateTime.Now,
                CpuUsage = 100,
                GpuUsage = 100,
                CpuTemperature = 98, // Very high temperature
                GpuTemperature = 97, // Very high temperature
                RamUsage = 14,
                FPS = 60,
                FrameTime = 16
            };

            _mockPerformanceMonitoringService.Setup(m => m.GetCurrentMetrics())
                .Returns(highTempMetrics);

            // Act
            var result = await _testingService.TestOptimizationAsync(_testOptimization, 5);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsSuccessful);
            Assert.AreEqual(_testOptimization.Name, result.OptimizationName);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage.Contains("Stress test failed"));

            // Verify that optimization was applied and then reverted
            _mockOptimizationService.Verify(m => m.ApplyOptimizationAsync(_testOptimization, true), Times.Once);
            _mockOptimizationService.Verify(m => m.RevertOptimizationAsync(_testOptimization), Times.Once);
        }

        [TestMethod]
        public async Task TestOptimizationAsync_WithPerformanceRegression_ShouldStillSucceed()
        {
            // Arrange
            // Setup optimized benchmark to return lower score
            var regressionResult = new CircleUtility.Services.BenchmarkPerformanceResult
            {
                BenchmarkType = BenchmarkType.Quick,
                Score = 950, // 5% regression
                Duration = TimeSpan.FromSeconds(5),
                AverageFps = 95,
                MinimumFps = 85,
                MaximumFps = 105,
                FrameTimeAvg = 11,
                FrameTime99Percentile = 16,
                IsSuccessful = true
            };

            // First call returns base score, second call returns regression score
            int callCount = 0;
            _mockBenchmarkingService.Setup(m => m.RunBenchmarkAsync(It.IsAny<BenchmarkType>()))
                .ReturnsAsync(() => {
                    callCount++;
                    return callCount == 1 ?
                        new CircleUtility.Services.BenchmarkPerformanceResult
                        {
                            BenchmarkType = BenchmarkType.Quick,
                            Score = 1000,
                            IsSuccessful = true
                        } : regressionResult;
                });

            _mockBenchmarkingService.Setup(m => m.RunBenchmarkAsync(It.IsAny<BenchmarkType>(), It.IsAny<System.Threading.CancellationToken>()))
                .ReturnsAsync(() => {
                    return callCount == 1 ?
                        new CircleUtility.Services.BenchmarkPerformanceResult
                        {
                            BenchmarkType = BenchmarkType.Quick,
                            Score = 1000,
                            IsSuccessful = true
                        } : regressionResult;
                });

            // Act
            var result = await _testingService.TestOptimizationAsync(_testOptimization, 5);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsSuccessful); // Test should still succeed
            Assert.AreEqual(_testOptimization.Name, result.OptimizationName);
            Assert.IsTrue(result.PerformanceImpact < 0); // Negative impact

            // Verify that optimization was applied and then reverted
            _mockOptimizationService.Verify(m => m.ApplyOptimizationAsync(_testOptimization, true), Times.Once);
            _mockOptimizationService.Verify(m => m.RevertOptimizationAsync(_testOptimization), Times.Once);
        }

        [TestMethod]
        public void CancelTest_ShouldCancelOngoingTest()
        {
            // Arrange
            bool testStartedCalled = false;
            bool testCancelledCalled = false;

            _testingService.TestStarted += (sender, e) => { testStartedCalled = true; };
            _testingService.TestCancelled += (sender, e) => { testCancelledCalled = true; };

            // Setup benchmarking to take a long time
            _mockBenchmarkingService.Setup(m => m.RunBenchmarkAsync(It.IsAny<BenchmarkType>()))
                .Returns(async (BenchmarkType type) =>
                {
                    await Task.Delay(1000); // Delay to allow cancellation
                    return new CircleUtility.Services.BenchmarkPerformanceResult
                    {
                        BenchmarkType = type,
                        Score = 1000,
                        IsSuccessful = true
                    };
                });

            _mockBenchmarkingService.Setup(m => m.RunBenchmarkAsync(It.IsAny<BenchmarkType>(), It.IsAny<System.Threading.CancellationToken>()))
                .Returns(async (BenchmarkType type, System.Threading.CancellationToken token) =>
                {
                    await Task.Delay(1000, token); // Delay to allow cancellation
                    return new CircleUtility.Services.BenchmarkPerformanceResult
                    {
                        BenchmarkType = type,
                        Score = 1000,
                        IsSuccessful = true
                    };
                });

            // Act
            var testTask = _testingService.TestOptimizationAsync(_testOptimization, 30);

            // Wait a bit to ensure the test has started
            Task.Delay(100).Wait();

            // Cancel the test
            _testingService.CancelTest();

            // Wait for the test to complete or timeout
            try
            {
                testTask.Wait(5000);
            }
            catch (AggregateException)
            {
                // Task may throw if cancelled
            }

            // Assert
            Assert.IsTrue(testStartedCalled, "TestStarted event should have been raised");
            Assert.IsTrue(testCancelledCalled, "TestCancelled event should have been raised");
            Assert.IsFalse(_testingService.IsTestingInProgress, "Testing should no longer be in progress");

            // Verify that optimization was reverted if it was applied
            _mockOptimizationService.Verify(m => m.RevertOptimizationAsync(_testOptimization), Times.AtMostOnce);
        }
    }
}

