??using System;
using System.Windows;
using CircleUtility.Interfaces;
using CircleUtility.Models;

namespace CircleUtility.Tests.Mocks
{
    /// <summary>
    /// Mock implementation of IUIFactory for testing
    /// </summary>
    public class MockUIFactory : IUIFactory
    {
        /// <summary>
        /// Gets the thermal warning dialog
        /// </summary>
        public MockThermalWarningDialog ThermalWarningDialog { get; private set; } = null!;

        /// <summary>
        /// Gets the dialog service
        /// </summary>
        public MockDialogService DialogService { get; }

        /// <summary>
        /// Gets the notification service
        /// </summary>
        public MockNotificationService NotificationService { get; }

        /// <summary>
        /// Initializes a new instance of the MockUIFactory class
        /// </summary>
        public MockUIFactory()
        {
            DialogService = new MockDialogService();
            NotificationService = new MockNotificationService();
        }

        /// <summary>
        /// Creates a file dialog
        /// </summary>
        /// <param name="isOpenDialog">Whether this is an open dialog</param>
        /// <param name="filter">The file filter</param>
        /// <param name="initialDirectory">The initial directory</param>
        /// <param name="defaultFileName">The default file name</param>
        /// <param name="owner">The owner window</param>
        /// <returns>The file dialog</returns>
        public IDialog CreateFileDialog(bool isOpenDialog, string filter, string? initialDirectory = null, string? defaultFileName = null, Window? owner = null)
        {
            // Return a simple mock dialog
            return new MockDialog();
        }

        /// <summary>
        /// Creates a login screen
        /// </summary>
        /// <returns>The login screen</returns>
        

        /// <summary>
        /// Creates a message dialog
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="buttons">The buttons</param>
        /// <param name="owner">The owner window</param>
        /// <returns>The message dialog</returns>
        public IDialog CreateMessageDialog(string title, string message, MessageDialogButtons buttons, Window? owner = null)
        {
            // Return a simple mock dialog
            return new MockDialog();
        }

        /// <summary>
        /// Creates a notification panel
        /// </summary>
        /// <returns>The notification panel</returns>
        public INotificationPanel CreateNotificationPanel()
        {
            // Not implemented for this example
            throw new NotImplementedException();
        }

        /// <summary>
        /// Creates a performance monitor view
        /// </summary>
        /// <returns>The performance monitor view</returns>
        public IPerformanceMonitorView CreatePerformanceMonitorView()
        {
            // Not implemented for this example
            throw new NotImplementedException();
        }

        /// <summary>
        /// Creates a progress dialog
        /// </summary>
        /// <param name="title">The title</param>
        /// <param name="message">The message</param>
        /// <param name="isIndeterminate">Whether the progress is indeterminate</param>
        /// <param name="owner">The owner window</param>
        /// <returns>The progress dialog</returns>
        public IDialog CreateProgressDialog(string title, string message, bool isIndeterminate = false, Window? owner = null)
        {
            // Return a simple mock dialog
            return new MockDialog();
        }

        /// <summary>
        /// Creates a settings screen
        /// </summary>
        /// <returns>The settings screen</returns>
        public ISettingsScreen CreateSettingsScreen()
        {
            // Not implemented for this example
            throw new NotImplementedException();
        }

        /// <summary>
        /// Creates a thermal warning dialog
        /// </summary>
        /// <param name="profile">The power profile</param>
        /// <returns>The thermal warning dialog</returns>
        public IThermalWarningDialog CreateThermalWarningDialog(PowerManagementProfile profile)
        {
            ThermalWarningDialog = new MockThermalWarningDialog(profile);
            return ThermalWarningDialog;
        }
    }

    /// <summary>
    /// Simple mock dialog for testing
    /// </summary>
    public class MockDialog : IDialog
    {
        /// <summary>
        /// Gets or sets the dialog result
        /// </summary>
        public bool? DialogResult { get; set; }

        /// <summary>
        /// Gets a value indicating whether the component is visible
        /// </summary>
        public bool IsVisible { get; private set; }

        /// <summary>
        /// Closes the dialog
        /// </summary>
        public void Close()
        {
            IsVisible = false;
        }

        /// <summary>
        /// Hides the UI component
        /// </summary>
        public void Hide()
        {
            IsVisible = false;
        }

        /// <summary>
        /// Initializes the UI component
        /// </summary>
        public void Initialize()
        {
            // No-op for mock
        }

        /// <summary>
        /// Shows the UI component
        /// </summary>
        public void Show()
        {
            IsVisible = true;
        }

        /// <summary>
        /// Shows the dialog and waits for user interaction
        /// </summary>
        /// <returns>The dialog result</returns>
        public bool? ShowDialog()
        {
            IsVisible = true;
            return DialogResult;
        }
    }
}


