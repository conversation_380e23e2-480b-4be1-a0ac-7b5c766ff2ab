using System;
using System.Collections.Generic;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility.Tests.Mocks
{
    /// <summary>
    /// Mock implementation of ILogger for testing
    /// </summary>
    public class MockLoggerService : ILogger
    {
        public List<LogMessage> LogMessages { get; } = new List<LogMessage>();

        public void Log(string message, LogLevel level)
        {
            LogMessages.Add(new LogMessage { Message = message, Level = level });

            // Raise the event
            LogEntryAdded?.Invoke(this, new CircleUtility.Models.LogEntryEventArgs(new CircleUtility.Models.LogEntry
            {
                Message = message,
                Level = level,
                Timestamp = DateTime.Now,
                Exception = null
            }));
        }

        public class LogMessage
        {
            public string Message { get; set; } = string.Empty;
            public LogLevel Level { get; set; }
        }

        /// <summary>
        /// Event raised when a log entry is added
        /// </summary>
        public event EventHandler<CircleUtility.Models.LogEntryEventArgs>? LogEntryAdded;
    }
}
