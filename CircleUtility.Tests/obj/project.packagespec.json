"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\TheCircleUtility\\CircleUtility.Tests\\CircleUtility.Tests.csproj","projectName":"CircleUtility.Tests","projectPath":"C:\\Users\\<USER>\\Desktop\\TheCircleUtility\\CircleUtility.Tests\\CircleUtility.Tests.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\TheCircleUtility\\CircleUtility.Tests\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net6.0-windows"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net6.0-windows7.0":{"targetAlias":"net6.0-windows","projectReferences":{"C:\\Users\\<USER>\\Desktop\\TheCircleUtility\\CircleUtility\\CircleUtility.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\TheCircleUtility\\CircleUtility\\CircleUtility.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net6.0-windows7.0":{"targetAlias":"net6.0-windows","dependencies":{"MSTest.TestAdapter":{"target":"Package","version":"[3.0.2, )"},"MSTest.TestFramework":{"target":"Package","version":"[3.0.2, )"},"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.5.0, )"},"Moq":{"target":"Package","version":"[4.18.4, )"},"NUnit":{"target":"Package","version":"[4.3.2, )"},"NUnit3TestAdapter":{"target":"Package","version":"[5.0.0, )"},"coverlet.collector":{"target":"Package","version":"[3.2.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[6.0.36, 6.0.36]"},{"name":"Microsoft.NETCore.App.Ref","version":"[6.0.36, 6.0.36]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[6.0.36, 6.0.36]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"},"Microsoft.WindowsDesktop.App.WPF":{"privateAssets":"none"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}