using System.Windows;
using System.Windows.Data;
using CircleUtility.Controls;
using NUnit.Framework;
using Assert = NUnit.Framework.Assert;

namespace CircleUtility.Tests.Controls
{
    [TestFixture]
    [Apartment(System.Threading.ApartmentState.STA)]
    public class SystemHealthIndicatorTests
    {
        private SystemHealthIndicator _indicator = null!;

        [SetUp]
        public void SetUp()
        {
            _indicator = new SystemHealthIndicator();
        }

        [Test]
        public void Score_Property_Should_Support_Binding()
        {
            // Arrange
            var testObject = new TestBindingSource { SystemHealthScore = 75 };
            
            // Create a binding
            var binding = new Binding("SystemHealthScore")
            {
                Source = testObject,
                Mode = BindingMode.OneWay
            };

            // Act
            BindingOperations.SetBinding(_indicator, SystemHealthIndicator.ScoreProperty, binding);

            // Assert
            NUnit.Framework.Assert.That(_indicator.Score, "Score should be bound to the source property", NUnit.Framework.Is.EqualTo(75));
        }

        [Test]
        public void Score_Property_Should_Update_When_Source_Changes()
        {
            // Arrange
            var testObject = new TestBindingSource { SystemHealthScore = 50 };
            
            var binding = new Binding("SystemHealthScore")
            {
                Source = testObject,
                Mode = BindingMode.OneWay
            };

            BindingOperations.SetBinding(_indicator, SystemHealthIndicator.ScoreProperty, binding);

            // Act
            testObject.SystemHealthScore = 90;

            // Assert
            NUnit.Framework.Assert.That(_indicator.Score, "Score should update when source property changes", NUnit.Framework.Is.EqualTo(90));
        }

        [Test]
        public void IsAnimating_Property_Should_Support_Binding()
        {
            // Arrange
            var testObject = new TestBindingSource { IsAnimating = false };
            
            var binding = new Binding("IsAnimating")
            {
                Source = testObject,
                Mode = BindingMode.OneWay
            };

            // Act
            BindingOperations.SetBinding(_indicator, SystemHealthIndicator.IsAnimatingProperty, binding);

            // Assert
            NUnit.Framework.Assert.That(_indicator.IsAnimating, "IsAnimating should be bound to the source property", NUnit.Framework.Is.EqualTo(false));
        }

        [Test]
        public void Score_Property_Should_Clamp_Values()
        {
            // Test upper bound
            _indicator.Score = 150;
            NUnit.Framework.Assert.That(_indicator.Score, "Score should be clamped to maximum of 100", NUnit.Framework.Is.EqualTo(100));

            // Test lower bound
            _indicator.Score = -50;
            NUnit.Framework.Assert.That(_indicator.Score, "Score should be clamped to minimum of 0", NUnit.Framework.Is.EqualTo(0));

            // Test valid range
            _indicator.Score = 75;
            NUnit.Framework.Assert.That(_indicator.Score, "Score should accept valid values", NUnit.Framework.Is.EqualTo(75));
        }

        private class TestBindingSource : System.ComponentModel.INotifyPropertyChanged
        {
            private int _systemHealthScore;
            private bool _isAnimating = true;

            public int SystemHealthScore
            {
                get => _systemHealthScore;
                set
                {
                    _systemHealthScore = value;
                    PropertyChanged?.Invoke(this, new System.ComponentModel.PropertyChangedEventArgs(nameof(SystemHealthScore)));
                }
            }

            public bool IsAnimating
            {
                get => _isAnimating;
                set
                {
                    _isAnimating = value;
                    PropertyChanged?.Invoke(this, new System.ComponentModel.PropertyChangedEventArgs(nameof(IsAnimating)));
                }
            }

            public event System.ComponentModel.PropertyChangedEventHandler? PropertyChanged;
        }
    }
}





