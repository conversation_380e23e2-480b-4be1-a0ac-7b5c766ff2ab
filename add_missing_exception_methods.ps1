# Add missing methods to ExceptionHandlingService
Write-Host "Adding missing methods to ExceptionHandlingService..."

$exceptionPath = "CircleUtility\Services\ExceptionHandlingService.cs"
$exceptionContent = Get-Content $exceptionPath -Raw

# Add the missing ExecuteWithHandling methods before the DetermineExceptionSeverity method
$missingMethods = @'

        /// <summary>
        /// Handles an exception and returns a default value
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">The context where the exception occurred</param>
        /// <param name="defaultValue">The default value to return</param>
        /// <param name="showToUser">Whether to show the error to the user</param>
        /// <returns>The default value</returns>
        public T HandleException<T>(Exception exception, string context, T defaultValue, bool showToUser = false)
        {
            HandleException(exception, context, showToUser);
            return defaultValue;
        }

        /// <summary>
        /// Executes an action with standardized exception handling
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="context">The context for error reporting</param>
        /// <param name="showErrorsToUser">Whether to show errors to the user</param>
        /// <returns>True if successful, false if an exception occurred</returns>
        public bool ExecuteWithHandling(Action action, string context, bool showErrorsToUser = false)
        {
            try
            {
                action();
                return true;
            }
            catch (Exception ex)
            {
                return HandleException(ex, context, showErrorsToUser);
            }
        }

        /// <summary>
        /// Executes a function with standardized exception handling
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="function">The function to execute</param>
        /// <param name="context">The context for error reporting</param>
        /// <param name="defaultValue">The default value to return on error</param>
        /// <param name="showErrorsToUser">Whether to show errors to the user</param>
        /// <returns>The function result or default value on error</returns>
        public T ExecuteWithHandling<T>(Func<T> function, string context, T defaultValue, bool showErrorsToUser = false)
        {
            try
            {
                return function();
            }
            catch (Exception ex)
            {
                HandleException(ex, context, showErrorsToUser);
                return defaultValue;
            }
        }
'@

# Insert the missing methods before the DetermineExceptionSeverity method
$exceptionContent = $exceptionContent -replace '        /// <summary>\s*/// Determines the severity of an exception', ($missingMethods + "`r`n`r`n        /// <summary>`r`n        /// Determines the severity of an exception")

Set-Content $exceptionPath -Value $exceptionContent

Write-Host "Missing methods added to ExceptionHandlingService!"
