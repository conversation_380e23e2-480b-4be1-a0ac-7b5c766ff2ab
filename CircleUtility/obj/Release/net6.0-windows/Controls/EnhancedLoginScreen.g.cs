#pragma checksum "..\..\..\..\Controls\EnhancedLoginScreen.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3D71E81C9D8CFCCABCB82A159CDD53B4FC92FF29"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Controls;
using CircleUtility.Models;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Controls {
    
    
    /// <summary>
    /// EnhancedLoginScreen
    /// </summary>
    public partial class EnhancedLoginScreen : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 262 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoginPanel;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordBox;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberMeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorMessage;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoginButton;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ForgotPasswordLink;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid TwoFactorPanel;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VerificationCodeBox;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TwoFactorErrorMessage;
        
        #line default
        #line hidden
        
        
        #line 335 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button VerifyButton;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResendCodeLink;
        
        #line default
        #line hidden
        
        
        #line 347 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BackToLoginLink;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TwoFactorStatusText;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ForgotPasswordPanel;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ResetUsernameBox;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResetErrorMessage;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResetBackToLoginLink;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResetStatusText;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingOverlay;
        
        #line default
        #line hidden
        
        
        #line 409 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse LoadingSpinner;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SuccessOverlay;
        
        #line default
        #line hidden
        
        
        #line 437 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path SuccessIcon;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;component/controls/enhancedloginscreen.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.LoginPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.UsernameBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 280 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.UsernameBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 287 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.PasswordBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RememberMeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.ErrorMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.LoginButton = ((System.Windows.Controls.Button)(target));
            
            #line 302 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.LoginButton.Click += new System.Windows.RoutedEventHandler(this.LoginButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ForgotPasswordLink = ((System.Windows.Controls.TextBlock)(target));
            
            #line 308 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.ForgotPasswordLink.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.ForgotPasswordLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TwoFactorPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.VerificationCodeBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 328 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.VerificationCodeBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TwoFactorErrorMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.VerifyButton = ((System.Windows.Controls.Button)(target));
            
            #line 338 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.VerifyButton.Click += new System.Windows.RoutedEventHandler(this.VerifyButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ResendCodeLink = ((System.Windows.Controls.TextBlock)(target));
            
            #line 344 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.ResendCodeLink.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.ResendCodeLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 15:
            this.BackToLoginLink = ((System.Windows.Controls.TextBlock)(target));
            
            #line 350 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.BackToLoginLink.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.BackToLoginLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TwoFactorStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ForgotPasswordPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 18:
            this.ResetUsernameBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 370 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.ResetUsernameBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InputBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ResetErrorMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 380 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ResetBackToLoginLink = ((System.Windows.Controls.TextBlock)(target));
            
            #line 386 "..\..\..\..\Controls\EnhancedLoginScreen.xaml"
            this.ResetBackToLoginLink.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.BackToLoginLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ResetStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.LoadingOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 24:
            this.LoadingSpinner = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 25:
            this.SuccessOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 26:
            this.SuccessIcon = ((System.Windows.Shapes.Path)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

