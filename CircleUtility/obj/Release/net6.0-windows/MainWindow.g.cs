#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9A9C11C23E42A1B0528FF93CF200C4EB447D5A07"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Converters;
using CircleUtility.Helpers;
using CircleUtility.ViewModels;
using CircleUtility.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 117 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainContent;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleShadow;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl NavMenu;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl ContentArea;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsernameText;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid NotificationPanel;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoginPanel;
        
        #line default
        #line hidden
        
        
        #line 422 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LoginUsernameInput;
        
        #line default
        #line hidden
        
        
        #line 442 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox LoginPasswordInput;
        
        #line default
        #line hidden
        
        
        #line 452 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LoginPasswordTextBox;
        
        #line default
        #line hidden
        
        
        #line 464 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TogglePasswordVisibility;
        
        #line default
        #line hidden
        
        
        #line 475 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PasswordToggleIcon;
        
        #line default
        #line hidden
        
        
        #line 506 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberMeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 552 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoginButton;
        
        #line default
        #line hidden
        
        
        #line 610 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoginStatusText;
        
        #line default
        #line hidden
        
        
        #line 626 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border RegisterPanel;
        
        #line default
        #line hidden
        
        
        #line 655 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RegisterUsernameInput;
        
        #line default
        #line hidden
        
        
        #line 675 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox RegisterPasswordInput;
        
        #line default
        #line hidden
        
        
        #line 685 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RegisterPasswordTextBox;
        
        #line default
        #line hidden
        
        
        #line 697 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RegisterTogglePasswordVisibility;
        
        #line default
        #line hidden
        
        
        #line 708 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RegisterPasswordToggleIcon;
        
        #line default
        #line hidden
        
        
        #line 746 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox RegisterConfirmPasswordInput;
        
        #line default
        #line hidden
        
        
        #line 758 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RegisterButton;
        
        #line default
        #line hidden
        
        
        #line 816 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RegisterStatusText;
        
        #line default
        #line hidden
        
        
        #line 832 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 840 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTitle;
        
        #line default
        #line hidden
        
        
        #line 857 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse LoadingSpinner;
        
        #line default
        #line hidden
        
        
        #line 865 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingStatus;
        
        #line default
        #line hidden
        
        
        #line 884 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar LoadingBar;
        
        #line default
        #line hidden
        
        
        #line 894 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressPercentage;
        
        #line default
        #line hidden
        
        
        #line 906 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusMessage;
        
        #line default
        #line hidden
        
        
        #line 918 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AccessButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.TitleShadow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.NavMenu = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 5:
            this.ContentArea = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 6:
            this.UsernameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.NotificationPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.LoginPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.LoginUsernameInput = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.LoginPasswordInput = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 11:
            this.LoginPasswordTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.TogglePasswordVisibility = ((System.Windows.Controls.Button)(target));
            
            #line 473 "..\..\..\MainWindow.xaml"
            this.TogglePasswordVisibility.Click += new System.Windows.RoutedEventHandler(this.TogglePasswordVisibility_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PasswordToggleIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.RememberMeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.LoginButton = ((System.Windows.Controls.Button)(target));
            
            #line 562 "..\..\..\MainWindow.xaml"
            this.LoginButton.Click += new System.Windows.RoutedEventHandler(this.LoginButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 596 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.RegisterLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 17:
            this.LoginStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.RegisterPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 19:
            this.RegisterUsernameInput = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.RegisterPasswordInput = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 21:
            this.RegisterPasswordTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.RegisterTogglePasswordVisibility = ((System.Windows.Controls.Button)(target));
            
            #line 706 "..\..\..\MainWindow.xaml"
            this.RegisterTogglePasswordVisibility.Click += new System.Windows.RoutedEventHandler(this.RegisterTogglePasswordVisibility_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.RegisterPasswordToggleIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.RegisterConfirmPasswordInput = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 25:
            this.RegisterButton = ((System.Windows.Controls.Button)(target));
            
            #line 768 "..\..\..\MainWindow.xaml"
            this.RegisterButton.Click += new System.Windows.RoutedEventHandler(this.RegisterButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 802 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.LoginLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 27:
            this.RegisterStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.LoadingPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 29:
            this.LoadingTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.LoadingSpinner = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 31:
            this.LoadingStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.LoadingBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 33:
            this.ProgressPercentage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.StatusMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.AccessButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

