#pragma checksum "..\..\..\..\Views\DashboardView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "22E251768B02442643786A8F0A31724D6F74D5A2"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Controls;
using CircleUtility.Models;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Views {
    
    
    /// <summary>
    /// DashboardView
    /// </summary>
    public partial class DashboardView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 54 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.SystemHealthIndicator SystemHealthIndicator;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.CircularGaugeControl CpuGauge;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.CircularGaugeControl RamGauge;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.CircularGaugeControl GpuGauge;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.CircularGaugeControl TempGauge;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.PerformanceGraphControl CpuGraph;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.PerformanceGraphControl RamGraph;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.PerformanceGraphControl GpuGraph;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.PerformanceGraphControl NetworkGraph;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.EnhancedHardwareBadgeControl CpuBadge;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.EnhancedHardwareBadgeControl GpuBadge;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.EnhancedHardwareBadgeControl RamBadge;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.EnhancedHardwareBadgeControl StorageBadge;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.EnhancedHardwareBadgeControl NetworkBadge;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.EnhancedHardwareBadgeControl SystemBadge;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.HardwareDetailPanel HardwareDetailPanel;
        
        #line default
        #line hidden
        
        
        #line 410 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.QuickActionPanel QuickActionPanel;
        
        #line default
        #line hidden
        
        
        #line 671 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.NotificationCenter NotificationCenter;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;V1.0.0.0;component/views/dashboardview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DashboardView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 7 "..\..\..\..\Views\DashboardView.xaml"
            ((CircleUtility.Views.DashboardView)(target)).Loaded += new System.Windows.RoutedEventHandler(this.OnLoaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SystemHealthIndicator = ((CircleUtility.Controls.SystemHealthIndicator)(target));
            return;
            case 3:
            this.CpuGauge = ((CircleUtility.Controls.CircularGaugeControl)(target));
            return;
            case 4:
            this.RamGauge = ((CircleUtility.Controls.CircularGaugeControl)(target));
            return;
            case 5:
            this.GpuGauge = ((CircleUtility.Controls.CircularGaugeControl)(target));
            return;
            case 6:
            this.TempGauge = ((CircleUtility.Controls.CircularGaugeControl)(target));
            return;
            case 7:
            this.CpuGraph = ((CircleUtility.Controls.PerformanceGraphControl)(target));
            return;
            case 8:
            this.RamGraph = ((CircleUtility.Controls.PerformanceGraphControl)(target));
            return;
            case 9:
            this.GpuGraph = ((CircleUtility.Controls.PerformanceGraphControl)(target));
            return;
            case 10:
            this.NetworkGraph = ((CircleUtility.Controls.PerformanceGraphControl)(target));
            return;
            case 11:
            this.CpuBadge = ((CircleUtility.Controls.EnhancedHardwareBadgeControl)(target));
            return;
            case 12:
            this.GpuBadge = ((CircleUtility.Controls.EnhancedHardwareBadgeControl)(target));
            return;
            case 13:
            this.RamBadge = ((CircleUtility.Controls.EnhancedHardwareBadgeControl)(target));
            return;
            case 14:
            this.StorageBadge = ((CircleUtility.Controls.EnhancedHardwareBadgeControl)(target));
            return;
            case 15:
            this.NetworkBadge = ((CircleUtility.Controls.EnhancedHardwareBadgeControl)(target));
            return;
            case 16:
            this.SystemBadge = ((CircleUtility.Controls.EnhancedHardwareBadgeControl)(target));
            return;
            case 17:
            this.HardwareDetailPanel = ((CircleUtility.Controls.HardwareDetailPanel)(target));
            return;
            case 18:
            this.QuickActionPanel = ((CircleUtility.Controls.QuickActionPanel)(target));
            return;
            case 19:
            this.NotificationCenter = ((CircleUtility.Controls.NotificationCenter)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

