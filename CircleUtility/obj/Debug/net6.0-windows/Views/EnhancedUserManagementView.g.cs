#pragma checksum "..\..\..\..\Views\EnhancedUserManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E58D710373345B589E4EB92A62A666E21AF20BF3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Views {
    
    
    /// <summary>
    /// EnhancedUserManagementView
    /// </summary>
    public partial class EnhancedUserManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 85 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RegularUserToggle;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AdminUserToggle;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid UserCreationForm;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NewUsernameInput;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox NewPasswordInput;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NewDisplayNameInput;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateUserButton;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusMessage;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl RegularUsersList;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl AdminUsersList;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;component/views/enhancedusermanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RegularUserToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 92 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.RegularUserToggle.Click += new System.Windows.RoutedEventHandler(this.RegularUserToggle_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AdminUserToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 99 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.AdminUserToggle.Click += new System.Windows.RoutedEventHandler(this.AdminUserToggle_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.UserCreationForm = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.NewUsernameInput = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.NewPasswordInput = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 6:
            this.NewDisplayNameInput = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.CreateUserButton = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.CreateUserButton.Click += new System.Windows.RoutedEventHandler(this.CreateUserButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.StatusMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.RegularUsersList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 10:
            this.AdminUsersList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

