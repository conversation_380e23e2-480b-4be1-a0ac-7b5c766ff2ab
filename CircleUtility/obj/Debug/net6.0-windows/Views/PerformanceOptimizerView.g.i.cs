#pragma checksum "..\..\..\..\Views\PerformanceOptimizerView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5D596FD12C9BEFB8CD82B3CB49D11A5BC1453254"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Controls;
using CircleUtility.Converters;
using CircleUtility.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Views {
    
    
    /// <summary>
    /// PerformanceOptimizerView
    /// </summary>
    public partial class PerformanceOptimizerView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 146 "..\..\..\..\Views\PerformanceOptimizerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.AnimatedPerformanceSlider SystemPerformanceSlider;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\Views\PerformanceOptimizerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.AnimatedPerformanceSlider CpuPerformanceSlider;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\PerformanceOptimizerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.AnimatedPerformanceSlider GpuPerformanceSlider;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\Views\PerformanceOptimizerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.AnimatedPerformanceSlider MemoryPerformanceSlider;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\Views\PerformanceOptimizerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CircleUtility.Controls.AnimatedPerformanceSlider StoragePerformanceSlider;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Views\PerformanceOptimizerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;V1.0.0.0;component/views/performanceoptimizerview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\PerformanceOptimizerView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SystemPerformanceSlider = ((CircleUtility.Controls.AnimatedPerformanceSlider)(target));
            return;
            case 2:
            this.CpuPerformanceSlider = ((CircleUtility.Controls.AnimatedPerformanceSlider)(target));
            return;
            case 3:
            this.GpuPerformanceSlider = ((CircleUtility.Controls.AnimatedPerformanceSlider)(target));
            return;
            case 4:
            this.MemoryPerformanceSlider = ((CircleUtility.Controls.AnimatedPerformanceSlider)(target));
            return;
            case 5:
            this.StoragePerformanceSlider = ((CircleUtility.Controls.AnimatedPerformanceSlider)(target));
            return;
            case 6:
            this.ApplyButton = ((System.Windows.Controls.Button)(target));
            
            #line 231 "..\..\..\..\Views\PerformanceOptimizerView.xaml"
            this.ApplyButton.Click += new System.Windows.RoutedEventHandler(this.ApplyButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

