#pragma checksum "..\..\..\WelcomeScreen_backup.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BE7F4CF6FD491B648E7CAC94B55C8021B87AD149"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility {
    
    
    /// <summary>
    /// WelcomeScreen
    /// </summary>
    public partial class WelcomeScreen : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 170 "..\..\..\WelcomeScreen_backup.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleShadow;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\WelcomeScreen_backup.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\WelcomeScreen_backup.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusMessage;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\WelcomeScreen_backup.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameInput;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\WelcomeScreen_backup.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingBar;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\WelcomeScreen_backup.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingStatus;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\WelcomeScreen_backup.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AccessButton;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\WelcomeScreen_backup.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;component/welcomescreen_backup.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\WelcomeScreen_backup.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleShadow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.StatusMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.UsernameInput = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.LoadingBar = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.LoadingStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AccessButton = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 278 "..\..\..\WelcomeScreen_backup.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

