#pragma checksum "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CC3CC6BC3472FDABB7668046AE7517DF61D9A1C5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Controls {
    
    
    /// <summary>
    /// AnimatedLoadingSpinner
    /// </summary>
    public partial class AnimatedLoadingSpinner : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse OuterCircle;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.Effects.DropShadowEffect GlowEffect;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.RotateTransform SpinnerRotation;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path Segment1;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path Segment2;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path Segment3;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path Segment4;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path Segment5;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path Segment6;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CenterText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;V1.0.0.0;component/controls/animatedloadingspinner.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\..\Controls\AnimatedLoadingSpinner.xaml"
            ((CircleUtility.Controls.AnimatedLoadingSpinner)(target)).Loaded += new System.Windows.RoutedEventHandler(this.UserControl_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.OuterCircle = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 3:
            this.GlowEffect = ((System.Windows.Media.Effects.DropShadowEffect)(target));
            return;
            case 4:
            this.SpinnerRotation = ((System.Windows.Media.RotateTransform)(target));
            return;
            case 5:
            this.Segment1 = ((System.Windows.Shapes.Path)(target));
            return;
            case 6:
            this.Segment2 = ((System.Windows.Shapes.Path)(target));
            return;
            case 7:
            this.Segment3 = ((System.Windows.Shapes.Path)(target));
            return;
            case 8:
            this.Segment4 = ((System.Windows.Shapes.Path)(target));
            return;
            case 9:
            this.Segment5 = ((System.Windows.Shapes.Path)(target));
            return;
            case 10:
            this.Segment6 = ((System.Windows.Shapes.Path)(target));
            return;
            case 11:
            this.CenterText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

