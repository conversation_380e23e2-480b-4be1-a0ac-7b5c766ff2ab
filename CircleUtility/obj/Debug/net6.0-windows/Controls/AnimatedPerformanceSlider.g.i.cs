#pragma checksum "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9EDE80D94574FA670AE53D5233E1D56F7241D801"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Controls {
    
    
    /// <summary>
    /// AnimatedPerformanceSlider
    /// </summary>
    public partial class AnimatedPerformanceSlider : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 216 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValueDisplay;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider MainSlider;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfileDisplay;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PowerSaverButton;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BalancedButton;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PerformanceButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;V1.0.0.0;component/controls/animatedperformanceslider.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
            ((CircleUtility.Controls.AnimatedPerformanceSlider)(target)).Loaded += new System.Windows.RoutedEventHandler(this.UserControl_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ValueDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.MainSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 249 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
            this.MainSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.MainSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ProfileDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.PowerSaverButton = ((System.Windows.Controls.Button)(target));
            
            #line 282 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
            this.PowerSaverButton.Click += new System.Windows.RoutedEventHandler(this.PowerSaverButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BalancedButton = ((System.Windows.Controls.Button)(target));
            
            #line 294 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
            this.BalancedButton.Click += new System.Windows.RoutedEventHandler(this.BalancedButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PerformanceButton = ((System.Windows.Controls.Button)(target));
            
            #line 305 "..\..\..\..\Controls\AnimatedPerformanceSlider.xaml"
            this.PerformanceButton.Click += new System.Windows.RoutedEventHandler(this.PerformanceButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

