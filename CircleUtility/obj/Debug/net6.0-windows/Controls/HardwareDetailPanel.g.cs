#pragma checksum "..\..\..\..\Controls\HardwareDetailPanel.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6750EA4B2208C04461AF122B60A1560A30F8D334"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Controls {
    
    
    /// <summary>
    /// HardwareDetailPanel
    /// </summary>
    public partial class HardwareDetailPanel : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 81 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CpuExpandButton;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CpuDetailsGrid;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuNameValue;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuCoresValue;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuClockSpeedValue;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuArchitectureValue;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuL2CacheValue;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuL3CacheValue;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GpuExpandButton;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GpuDetailsGrid;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GpuNameValue;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GpuMemoryValue;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GpuVendorValue;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GpuDriverValue;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RamExpandButton;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid RamDetailsGrid;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RamCapacityValue;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RamTypeValue;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RamSpeedValue;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StorageExpandButton;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel StorageDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NetworkExpandButton;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NetworkDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OsExpandButton;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid OsDetailsGrid;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OsNameValue;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OsVersionValue;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OsBuildValue;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;component/controls/hardwaredetailpanel.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CpuExpandButton = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
            this.CpuExpandButton.Click += new System.Windows.RoutedEventHandler(this.CpuExpandButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CpuDetailsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.CpuNameValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.CpuCoresValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CpuClockSpeedValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.CpuArchitectureValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CpuL2CacheValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.CpuL3CacheValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.GpuExpandButton = ((System.Windows.Controls.Button)(target));
            
            #line 135 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
            this.GpuExpandButton.Click += new System.Windows.RoutedEventHandler(this.GpuExpandButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.GpuDetailsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.GpuNameValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.GpuMemoryValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.GpuVendorValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.GpuDriverValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.RamExpandButton = ((System.Windows.Controls.Button)(target));
            
            #line 178 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
            this.RamExpandButton.Click += new System.Windows.RoutedEventHandler(this.RamExpandButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.RamDetailsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 17:
            this.RamCapacityValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.RamTypeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.RamSpeedValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.StorageExpandButton = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
            this.StorageExpandButton.Click += new System.Windows.RoutedEventHandler(this.StorageExpandButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.StorageDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.NetworkExpandButton = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
            this.NetworkExpandButton.Click += new System.Windows.RoutedEventHandler(this.NetworkExpandButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.NetworkDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 24:
            this.OsExpandButton = ((System.Windows.Controls.Button)(target));
            
            #line 259 "..\..\..\..\Controls\HardwareDetailPanel.xaml"
            this.OsExpandButton.Click += new System.Windows.RoutedEventHandler(this.OsExpandButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.OsDetailsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 26:
            this.OsNameValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.OsVersionValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.OsBuildValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

