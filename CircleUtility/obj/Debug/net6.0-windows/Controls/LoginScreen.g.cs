#pragma checksum "..\..\..\..\Controls\LoginScreen.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A7CACBCF8FB3572E47CFDD64DABD6953C07228CE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Controls {
    
    
    /// <summary>
    /// LoginScreen
    /// </summary>
    public partial class LoginScreen : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 214 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoginPanel;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordBox;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberMeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoginButton;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ForgotPasswordLink;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorMessage;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ResetPasswordPanel;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ResetUsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetPasswordButton;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BackToLoginLink;
        
        #line default
        #line hidden
        
        
        #line 415 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResetErrorMessage;
        
        #line default
        #line hidden
        
        
        #line 427 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SuccessPanel;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path SuccessIcon;
        
        #line default
        #line hidden
        
        
        #line 477 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingOverlay;
        
        #line default
        #line hidden
        
        
        #line 492 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse LoadingSpinner;
        
        #line default
        #line hidden
        
        
        #line 509 "..\..\..\..\Controls\LoginScreen.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;component/controls/loginscreen.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\LoginScreen.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 223 "..\..\..\..\Controls\LoginScreen.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.LoginPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.UsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.PasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 291 "..\..\..\..\Controls\LoginScreen.xaml"
            this.PasswordBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.PasswordBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RememberMeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.LoginButton = ((System.Windows.Controls.Button)(target));
            
            #line 306 "..\..\..\..\Controls\LoginScreen.xaml"
            this.LoginButton.Click += new System.Windows.RoutedEventHandler(this.LoginButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ForgotPasswordLink = ((System.Windows.Controls.TextBlock)(target));
            
            #line 318 "..\..\..\..\Controls\LoginScreen.xaml"
            this.ForgotPasswordLink.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.ForgotPasswordLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ErrorMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ResetPasswordPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.ResetUsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ResetPasswordButton = ((System.Windows.Controls.Button)(target));
            
            #line 388 "..\..\..\..\Controls\LoginScreen.xaml"
            this.ResetPasswordButton.Click += new System.Windows.RoutedEventHandler(this.ResetPasswordButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BackToLoginLink = ((System.Windows.Controls.TextBlock)(target));
            
            #line 400 "..\..\..\..\Controls\LoginScreen.xaml"
            this.BackToLoginLink.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.BackToLoginLink_MouseDown);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ResetErrorMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.SuccessPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.SuccessIcon = ((System.Windows.Shapes.Path)(target));
            return;
            case 17:
            this.LoadingOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 18:
            this.LoadingSpinner = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 19:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

