#pragma checksum "..\..\..\..\Controls\QuickActionPanel.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EC38F0095271863FE64BB720BAA69C14AA63DEC0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using CircleUtility.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Controls {
    
    
    /// <summary>
    /// QuickActionPanel
    /// </summary>
    public partial class QuickActionPanel : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 96 "..\..\..\..\Controls\QuickActionPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TweakSystemButton;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Controls\QuickActionPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RevertTweaksButton;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Controls\QuickActionPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizeInputButton;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Controls\QuickActionPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizeGpuButton;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\Controls\QuickActionPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizeNetworkButton;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\Controls\QuickActionPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CleanSystemButton;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\Controls\QuickActionPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizePerformanceButton;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\Controls\QuickActionPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestNotificationsButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;component/controls/quickactionpanel.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\QuickActionPanel.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TweakSystemButton = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.TweakSystemButton.Click += new System.Windows.RoutedEventHandler(this.TweakSystemButton_Click);
            
            #line default
            #line hidden
            
            #line 100 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.TweakSystemButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.Button_MouseEnter);
            
            #line default
            #line hidden
            
            #line 101 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.TweakSystemButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.Button_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RevertTweaksButton = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.RevertTweaksButton.Click += new System.Windows.RoutedEventHandler(this.RevertTweaksButton_Click);
            
            #line default
            #line hidden
            
            #line 126 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.RevertTweaksButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.Button_MouseEnter);
            
            #line default
            #line hidden
            
            #line 127 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.RevertTweaksButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.Button_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 3:
            this.OptimizeInputButton = ((System.Windows.Controls.Button)(target));
            
            #line 153 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeInputButton.Click += new System.Windows.RoutedEventHandler(this.OptimizeInputButton_Click);
            
            #line default
            #line hidden
            
            #line 154 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeInputButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.Button_MouseEnter);
            
            #line default
            #line hidden
            
            #line 155 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeInputButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.Button_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 4:
            this.OptimizeGpuButton = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeGpuButton.Click += new System.Windows.RoutedEventHandler(this.OptimizeGpuButton_Click);
            
            #line default
            #line hidden
            
            #line 180 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeGpuButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.Button_MouseEnter);
            
            #line default
            #line hidden
            
            #line 181 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeGpuButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.Button_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 5:
            this.OptimizeNetworkButton = ((System.Windows.Controls.Button)(target));
            
            #line 205 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeNetworkButton.Click += new System.Windows.RoutedEventHandler(this.OptimizeNetworkButton_Click);
            
            #line default
            #line hidden
            
            #line 206 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeNetworkButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.Button_MouseEnter);
            
            #line default
            #line hidden
            
            #line 207 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizeNetworkButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.Button_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CleanSystemButton = ((System.Windows.Controls.Button)(target));
            
            #line 231 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.CleanSystemButton.Click += new System.Windows.RoutedEventHandler(this.CleanSystemButton_Click);
            
            #line default
            #line hidden
            
            #line 232 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.CleanSystemButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.Button_MouseEnter);
            
            #line default
            #line hidden
            
            #line 233 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.CleanSystemButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.Button_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 7:
            this.OptimizePerformanceButton = ((System.Windows.Controls.Button)(target));
            
            #line 257 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizePerformanceButton.Click += new System.Windows.RoutedEventHandler(this.OptimizePerformanceButton_Click);
            
            #line default
            #line hidden
            
            #line 258 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizePerformanceButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.Button_MouseEnter);
            
            #line default
            #line hidden
            
            #line 259 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.OptimizePerformanceButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.Button_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TestNotificationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 283 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.TestNotificationsButton.Click += new System.Windows.RoutedEventHandler(this.TestNotificationsButton_Click);
            
            #line default
            #line hidden
            
            #line 284 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.TestNotificationsButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.Button_MouseEnter);
            
            #line default
            #line hidden
            
            #line 285 "..\..\..\..\Controls\QuickActionPanel.xaml"
            this.TestNotificationsButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.Button_MouseLeave);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

