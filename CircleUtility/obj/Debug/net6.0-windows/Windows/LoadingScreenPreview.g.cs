#pragma checksum "..\..\..\..\Windows\LoadingScreenPreview.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "489AD7BB6B8A94B086088C44DC47BFB1A87C8CF0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CircleUtility.Windows {
    
    
    /// <summary>
    /// LoadingScreenPreview
    /// </summary>
    public partial class LoadingScreenPreview : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTitle;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse LoadingSpinner;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingStatusText;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar LoadingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressPercentage;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingDetailsText;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AccessButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CircleUtility;component/windows/loadingscreenpreview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.LoadingTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.LoadingSpinner = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 4:
            this.LoadingStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.LoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 6:
            this.ProgressPercentage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.LoadingDetailsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.AccessButton = ((System.Windows.Controls.Button)(target));
            
            #line 146 "..\..\..\..\Windows\LoadingScreenPreview.xaml"
            this.AccessButton.Click += new System.Windows.RoutedEventHandler(this.AccessButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

