<UserControl x:Class="CircleUtility.Views.EnhancedUserManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="Black">
    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- Toggle Button Style -->
        <Style x:Key="ToggleButtonStyle" TargetType="ToggleButton">
            <Setter Property="Background" Value="#FF001428"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="BorderBrush" Value="#FF00C8FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Setter Property="Background" Value="#FF00C8FF"/>
                    <Setter Property="Foreground" Value="Black"/>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#FF00E8FF"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- User Card Style -->
        <Style x:Key="UserCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF0A141E"/>
            <Setter Property="BorderBrush" Value="#FF004080"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="CornerRadius" Value="3"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="Enhanced User Management"
                   FontFamily="Consolas"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,20"
                   HorizontalAlignment="Center">
            <TextBlock.Effect>
                <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
            </TextBlock.Effect>
        </TextBlock>

        <!-- Toggle Menu for Adding Users -->
        <Border Grid.Row="1" 
                Background="#FF050A0F"
                BorderBrush="#FF00C8FF"
                BorderThickness="1"
                Padding="15"
                Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Toggle Header -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                    <TextBlock Text="Add New Account: "
                               FontFamily="Consolas"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="White"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>

                    <ToggleButton x:Name="RegularUserToggle"
                                  Content="Regular User"
                                  Style="{StaticResource ToggleButtonStyle}"
                                  Width="120"
                                  Height="35"
                                  Margin="0,0,10,0"
                                  IsChecked="True"
                                  Click="RegularUserToggle_Click"/>

                    <ToggleButton x:Name="AdminUserToggle"
                                  Content="Admin User"
                                  Style="{StaticResource ToggleButtonStyle}"
                                  Width="120"
                                  Height="35"
                                  Click="AdminUserToggle_Click"/>
                </StackPanel>

                <!-- User Creation Form -->
                <Grid Grid.Row="1" x:Name="UserCreationForm">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Username -->
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="Username:"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,5"/>
                        <TextBox x:Name="NewUsernameInput"
                                 Background="#FF0A141E"
                                 Foreground="White"
                                 BorderBrush="#FF00C8FF"
                                 BorderThickness="1"
                                 Height="30"
                                 Padding="5"/>
                    </StackPanel>

                    <!-- Password -->
                    <StackPanel Grid.Column="1" Margin="0,0,10,0">
                        <TextBlock Text="Password:"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,5"/>
                        <PasswordBox x:Name="NewPasswordInput"
                                     Background="#FF0A141E"
                                     Foreground="White"
                                     BorderBrush="#FF00C8FF"
                                     BorderThickness="1"
                                     Height="30"
                                     Padding="5"/>
                    </StackPanel>

                    <!-- Display Name -->
                    <StackPanel Grid.Column="2" Margin="0,0,10,0">
                        <TextBlock Text="Display Name:"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   Foreground="#FF00C8FF"
                                   Margin="0,0,0,5"/>
                        <TextBox x:Name="NewDisplayNameInput"
                                 Background="#FF0A141E"
                                 Foreground="White"
                                 BorderBrush="#FF00C8FF"
                                 BorderThickness="1"
                                 Height="30"
                                 Padding="5"/>
                    </StackPanel>

                    <!-- Create Button -->
                    <Button Grid.Column="3"
                            x:Name="CreateUserButton"
                            Content="Create Account"
                            Background="#FF001428"
                            Foreground="White"
                            BorderBrush="#FF00C8FF"
                            BorderThickness="1"
                            Width="120"
                            Height="30"
                            FontFamily="Consolas"
                            FontWeight="Bold"
                            Cursor="Hand"
                            Click="CreateUserButton_Click"/>
                </Grid>

                <!-- Status Message -->
                <TextBlock Grid.Row="2"
                           x:Name="StatusMessage"
                           FontFamily="Consolas"
                           FontSize="12"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"
                           Visibility="Collapsed"/>
            </Grid>
        </Border>

        <!-- Two-Column User Display -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Regular Users Column -->
            <Border Grid.Column="0" 
                    Background="#FF050A0F"
                    BorderBrush="#FF00C8FF"
                    BorderThickness="1"
                    Margin="0,0,10,0"
                    Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Regular Users Header -->
                    <TextBlock Text="Regular Users"
                               FontFamily="Consolas"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="#FF00FF00"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,15">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#FF00FF00" BlurRadius="5" ShadowDepth="0" Opacity="0.7"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <!-- Regular Users List -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ItemsControl x:Name="RegularUsersList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource UserCardStyle}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- Username -->
                                            <TextBlock Grid.Row="0"
                                                       Text="{Binding Username}"
                                                       FontFamily="Consolas"
                                                       FontSize="14"
                                                       FontWeight="Bold"
                                                       Foreground="White"/>

                                            <!-- Display Name -->
                                            <TextBlock Grid.Row="1"
                                                       Text="{Binding DisplayName}"
                                                       FontFamily="Segoe UI"
                                                       FontSize="12"
                                                       Foreground="#FFC0C0C0"
                                                       Margin="0,2,0,0"/>

                                            <!-- Last Login -->
                                            <TextBlock Grid.Row="2"
                                                       Text="{Binding LastLoginFormatted, StringFormat='Last Login: {0}'}"
                                                       FontFamily="Consolas"
                                                       FontSize="10"
                                                       Foreground="#FF808080"
                                                       Margin="0,5,0,0"/>

                                            <!-- Actions -->
                                            <StackPanel Grid.Row="3" 
                                                        Orientation="Horizontal" 
                                                        HorizontalAlignment="Right"
                                                        Margin="0,10,0,0">
                                                <Button Content="Edit"
                                                        Width="50"
                                                        Height="25"
                                                        Background="#FF001428"
                                                        Foreground="White"
                                                        BorderBrush="#FF00C8FF"
                                                        BorderThickness="1"
                                                        FontSize="10"
                                                        Margin="0,0,5,0"/>
                                                <Button Content="Delete"
                                                        Width="50"
                                                        Height="25"
                                                        Background="#FF001428"
                                                        Foreground="White"
                                                        BorderBrush="#FFFF3232"
                                                        BorderThickness="1"
                                                        FontSize="10"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- Admin Users Column -->
            <Border Grid.Column="1" 
                    Background="#FF050A0F"
                    BorderBrush="#FF00C8FF"
                    BorderThickness="1"
                    Margin="10,0,0,0"
                    Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Admin Users Header -->
                    <TextBlock Text="Admin Users"
                               FontFamily="Consolas"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="#FFFF8C00"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,15">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#FFFF8C00" BlurRadius="5" ShadowDepth="0" Opacity="0.7"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <!-- Admin Users List -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ItemsControl x:Name="AdminUsersList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource UserCardStyle}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- Username -->
                                            <TextBlock Grid.Row="0"
                                                       Text="{Binding Username}"
                                                       FontFamily="Consolas"
                                                       FontSize="14"
                                                       FontWeight="Bold"
                                                       Foreground="#FFFF8C00"/>

                                            <!-- Display Name -->
                                            <TextBlock Grid.Row="1"
                                                       Text="{Binding DisplayName}"
                                                       FontFamily="Segoe UI"
                                                       FontSize="12"
                                                       Foreground="#FFC0C0C0"
                                                       Margin="0,2,0,0"/>

                                            <!-- Last Login -->
                                            <TextBlock Grid.Row="2"
                                                       Text="{Binding LastLoginFormatted, StringFormat='Last Login: {0}'}"
                                                       FontFamily="Consolas"
                                                       FontSize="10"
                                                       Foreground="#FF808080"
                                                       Margin="0,5,0,0"/>

                                            <!-- Actions -->
                                            <StackPanel Grid.Row="3" 
                                                        Orientation="Horizontal" 
                                                        HorizontalAlignment="Right"
                                                        Margin="0,10,0,0">
                                                <Button Content="Edit"
                                                        Width="50"
                                                        Height="25"
                                                        Background="#FF001428"
                                                        Foreground="White"
                                                        BorderBrush="#FF00C8FF"
                                                        BorderThickness="1"
                                                        FontSize="10"
                                                        Margin="0,0,5,0"/>
                                                <Button Content="Delete"
                                                        Width="50"
                                                        Height="25"
                                                        Background="#FF001428"
                                                        Foreground="White"
                                                        BorderBrush="#FFFF3232"
                                                        BorderThickness="1"
                                                        FontSize="10"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
