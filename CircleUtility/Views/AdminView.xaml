<UserControl x:Class="CircleUtility.Views.AdminView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:CircleUtility.Converters"
             xmlns:views="clr-namespace:CircleUtility.Views"
             xmlns:viewmodels="clr-namespace:CircleUtility.ViewModels"
             Background="Black">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <local:StringEqualityToStyleConverter x:Key="StringEqualityToStyleConverter"/>
        <local:StringEqualityToVisibilityConverter x:Key="StringEqualityToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Text="Admin Control Panel"
                   FontFamily="Consolas"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="#FF00C8FF"
                   Margin="0,0,0,20"/>

        <!-- Status message -->
        <Border Grid.Row="1"
                Background="{Binding IsStatusSuccess, Converter={StaticResource BoolToColorConverter}}"
                BorderThickness="1"
                BorderBrush="{Binding IsStatusSuccess, Converter={StaticResource BoolToColorConverter}}"
                Padding="15"
                Margin="0,0,0,20"
                Visibility="{Binding IsStatusVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
            <TextBlock Text="{Binding StatusMessage}"
                       FontFamily="Consolas"
                       FontSize="14"
                       Foreground="White"/>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Tabs -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <Button Content="Users"
                        Style="{Binding SelectedTab, Converter={StaticResource StringEqualityToStyleConverter}, ConverterParameter='Users'}"
                        Command="{Binding SwitchTabCommand}"
                        CommandParameter="Users"/>

                <Button Content="Search"
                        Style="{Binding SelectedTab, Converter={StaticResource StringEqualityToStyleConverter}, ConverterParameter='Search'}"
                        Command="{Binding SwitchTabCommand}"
                        CommandParameter="Search"/>

                <Button Content="Punishments"
                        Style="{Binding SelectedTab, Converter={StaticResource StringEqualityToStyleConverter}, ConverterParameter='Punishments'}"
                        Command="{Binding SwitchTabCommand}"
                        CommandParameter="Punishments"/>

                <Button Content="Logs"
                        Style="{Binding SelectedTab, Converter={StaticResource StringEqualityToStyleConverter}, ConverterParameter='Logs'}"
                        Command="{Binding SwitchTabCommand}"
                        CommandParameter="Logs"/>

                <Button Content="System"
                        Style="{Binding SelectedTab, Converter={StaticResource StringEqualityToStyleConverter}, ConverterParameter='System'}"
                        Command="{Binding SwitchTabCommand}"
                        CommandParameter="System"/>

                <Button Content="Configuration"
                        Style="{Binding SelectedTab, Converter={StaticResource StringEqualityToStyleConverter}, ConverterParameter='Configuration'}"
                        Command="{Binding SwitchTabCommand}"
                        CommandParameter="Configuration"/>

                <Button Content="Updates"
                        Style="{Binding SelectedTab, Converter={StaticResource StringEqualityToStyleConverter}, ConverterParameter='Updates'}"
                        Command="{Binding SwitchTabCommand}"
                        CommandParameter="Updates"/>
            </StackPanel>

            <!-- Tab content -->
            <Grid Grid.Row="1">
                <!-- Loading indicator -->
                <Border Background="#80000000"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Panel.ZIndex="100">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="Loading..."
                                   FontFamily="Consolas"
                                   FontSize="18"
                                   Foreground="#FF00C8FF"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,10"/>
                        <ProgressBar Width="200"
                                     Height="10"
                                     IsIndeterminate="True"
                                     Background="#FF0A141E"
                                     Foreground="#FF00C8FF"
                                     BorderBrush="#FF00B4F0"
                                     BorderThickness="0"/>
                    </StackPanel>
                </Border>

                <!-- Users tab -->
                <Grid Visibility="{Binding SelectedTab, Converter={StaticResource StringEqualityToVisibilityConverter}, ConverterParameter='Users'}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Actions -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Content="Add User"
                                Command="{Binding AddUserCommand}"
                                Width="100"
                                Height="30"
                                Margin="0,0,10,0"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"/>

                        <Button Content="Edit User"
                                Command="{Binding EditUserCommand}"
                                Width="100"
                                Height="30"
                                Margin="0,0,10,0"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"/>

                        <Button Content="Delete User"
                                Command="{Binding DeleteUserCommand}"
                                Width="100"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FFFF3232"
                                BorderThickness="1"/>
                    </StackPanel>

                    <!-- Users list -->
                    <DataGrid Grid.Row="1"
                              ItemsSource="{Binding UserProfiles}"
                              SelectedItem="{Binding SelectedUser}"
                              AutoGenerateColumns="False"
                              Background="#FF050A0F"
                              BorderBrush="#FF00C8FF"
                              BorderThickness="1"
                              RowBackground="#FF0A141E"
                              AlternatingRowBackground="#FF050A0F"
                              HeadersVisibility="Column"
                              GridLinesVisibility="Horizontal"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="50"/>
                            <DataGridTextColumn Header="Username" Binding="{Binding Username}" Width="150"/>
                            <DataGridCheckBoxColumn Header="Admin" Binding="{Binding IsAdmin}" Width="80"/>
                            <DataGridTextColumn Header="Last Login" Binding="{Binding LastLogin}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>

                <!-- Search tab -->
                <Grid Visibility="{Binding SelectedTab, Converter={StaticResource StringEqualityToVisibilityConverter}, ConverterParameter='Search'}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Search header -->
                    <TextBlock Grid.Row="0"
                               Text="User Search"
                               FontFamily="Consolas"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="#FF00C8FF"
                               Margin="0,0,0,15">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <!-- Search options -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Search bar -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                     FontFamily="Consolas"
                                     FontSize="14"
                                     Foreground="White"
                                     Background="#FF050A0F"
                                     BorderBrush="#FF00C8FF"
                                     BorderThickness="1"
                                     Padding="5"
                                     Margin="0,0,10,0"/>

                            <Button Grid.Column="1"
                                    Content="Search"
                                    Command="{Binding SearchUsersCommand}"
                                    Width="100"
                                    Height="30"
                                    Background="#FF001428"
                                    Foreground="White"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"
                                    Margin="0,0,10,0"/>

                            <Button Grid.Column="2"
                                    Content="Clear"
                                    Command="{Binding ClearSearchCommand}"
                                    Width="80"
                                    Height="30"
                                    Background="#FF001428"
                                    Foreground="White"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"/>
                        </Grid>

                        <!-- Search options -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal">
                            <CheckBox Content="Search by Hardware ID"
                                      IsChecked="{Binding SearchByHardwareId}"
                                      Foreground="White"
                                      Margin="0,0,15,0"/>

                            <CheckBox Content="Search by IP"
                                      IsChecked="{Binding SearchByIp}"
                                      Foreground="White"
                                      Margin="0,0,15,0"/>

                            <CheckBox Content="Include Disabled Users"
                                      IsChecked="{Binding IncludeDisabled}"
                                      Foreground="White"
                                      Margin="0,0,15,0"/>

                            <CheckBox Content="Include Admins"
                                      IsChecked="{Binding IncludeAdmins}"
                                      Foreground="White"/>
                        </StackPanel>
                    </Grid>

                    <!-- Search results -->
                    <DataGrid Grid.Row="2"
                              ItemsSource="{Binding SearchResults}"
                              SelectedItem="{Binding SelectedSecurityUser}"
                              AutoGenerateColumns="False"
                              Background="#FF050A0F"
                              BorderBrush="#FF00C8FF"
                              BorderThickness="1"
                              RowBackground="#FF0A141E"
                              AlternatingRowBackground="#FF050A0F"
                              HeadersVisibility="Column"
                              GridLinesVisibility="Horizontal"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True"
                              Margin="0,0,0,15">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Username" Binding="{Binding Username}" Width="150"/>
                            <DataGridCheckBoxColumn Header="Admin" Binding="{Binding IsAdmin}" Width="60"/>
                            <DataGridCheckBoxColumn Header="Locked" Binding="{Binding IsLocked}" Width="60"/>
                            <DataGridTextColumn Header="Last Login" Binding="{Binding FormattedLastLoginDate}" Width="150"/>
                            <DataGridTextColumn Header="IP Address" Binding="{Binding LastIpAddress}" Width="120"/>
                            <DataGridTextColumn Header="Email" Binding="{Binding Email}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Action buttons -->
                    <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="Promote to Admin"
                                Command="{Binding PromoteToAdminCommand}"
                                Width="150"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"
                                Margin="0,0,10,0"/>

                        <Button Content="Add Hardware ID"
                                Command="{Binding AddHardwareIdCommand}"
                                Width="150"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"
                                Margin="0,0,10,0"/>

                        <Button Content="Add IP Address"
                                Command="{Binding AddIpAddressCommand}"
                                Width="150"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"
                                Margin="0,0,10,0"/>

                        <Button Content="View Punishment History"
                                Command="{Binding ViewPunishmentHistoryCommand}"
                                Width="180"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"
                                Margin="0,0,10,0"/>

                        <Button Content="View Users by Hardware ID"
                                Command="{Binding ViewUsersByHardwareIdCommand}"
                                Width="180"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"/>
                    </StackPanel>
                </Grid>

                <!-- Punishments tab -->
                <Grid Visibility="{Binding SelectedTab, Converter={StaticResource StringEqualityToVisibilityConverter}, ConverterParameter='Punishments'}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Punishment header -->
                    <TextBlock Grid.Row="0"
                               Text="User Punishment Management"
                               FontFamily="Consolas"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="#FF00C8FF"
                               Margin="0,0,0,15">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#FF00C8FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                        </TextBlock.Effect>
                    </TextBlock>

                    <!-- Apply punishment -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="Punishment Type:"
                                   FontFamily="Consolas"
                                   FontSize="14"
                                   Foreground="White"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>

                        <ComboBox Grid.Column="1"
                                  SelectedItem="{Binding SelectedPunishmentType}"
                                  Background="#FF050A0F"
                                  Foreground="White"
                                  BorderBrush="#FF00C8FF"
                                  BorderThickness="1"
                                  Margin="0,0,10,0">
                            <ComboBoxItem Content="Warning"/>
                            <ComboBoxItem Content="Mute"/>
                            <ComboBoxItem Content="TemporaryBan"/>
                            <ComboBoxItem Content="PermanentBan"/>
                            <ComboBoxItem Content="IpBan"/>
                            <ComboBoxItem Content="HardwareBan"/>
                            <ComboBoxItem Content="FeatureRestriction"/>
                        </ComboBox>

                        <TextBlock Grid.Column="2"
                                   Text="Expires:"
                                   FontFamily="Consolas"
                                   FontSize="14"
                                   Foreground="White"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>

                        <DatePicker Grid.Column="3"
                                    SelectedDate="{Binding PunishmentExpirationDate}"
                                    Background="#FF050A0F"
                                    Foreground="White"
                                    BorderBrush="#FF00C8FF"
                                    BorderThickness="1"
                                    Margin="0,0,10,0"/>

                        <Button Grid.Column="4"
                                Content="Apply Punishment"
                                Command="{Binding ApplyPunishmentCommand}"
                                Width="150"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FFFF3232"
                                BorderThickness="1"/>
                    </Grid>

                    <!-- Reason -->
                    <Grid Grid.Row="2" Margin="0,0,0,15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Text="Reason:"
                                   FontFamily="Consolas"
                                   FontSize="14"
                                   Foreground="White"
                                   Margin="0,0,0,5"/>

                        <TextBox Grid.Row="1"
                                 Text="{Binding PunishmentReason, UpdateSourceTrigger=PropertyChanged}"
                                 FontFamily="Consolas"
                                 FontSize="14"
                                 Foreground="White"
                                 Background="#FF050A0F"
                                 BorderBrush="#FF00C8FF"
                                 BorderThickness="1"
                                 Padding="5"
                                 Height="60"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 Margin="0,0,0,15"/>

                        <!-- Punishment history -->
                        <DataGrid Grid.Row="2"
                                  ItemsSource="{Binding PunishmentHistory}"
                                  AutoGenerateColumns="False"
                                  Background="#FF050A0F"
                                  BorderBrush="#FF00C8FF"
                                  BorderThickness="1"
                                  RowBackground="#FF0A141E"
                                  AlternatingRowBackground="#FF050A0F"
                                  HeadersVisibility="Column"
                                  GridLinesVisibility="Horizontal"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="100"/>
                                <DataGridTextColumn Header="Reason" Binding="{Binding Reason}" Width="200"/>
                                <DataGridTextColumn Header="Issued By" Binding="{Binding IssuedBy}" Width="100"/>
                                <DataGridTextColumn Header="Issued Date" Binding="{Binding FormattedIssuedDate}" Width="150"/>
                                <DataGridTextColumn Header="Expires" Binding="{Binding FormattedExpirationDate}" Width="150"/>
                                <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="80"/>
                                <DataGridTemplateColumn Header="Actions" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Content="Revoke"
                                                    Command="{Binding DataContext.RevokePunishmentCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding Id}"
                                                    Width="80"
                                                    Height="25"
                                                    Background="#FF001428"
                                                    Foreground="White"
                                                    BorderBrush="#FFFF3232"
                                                    BorderThickness="1"
                                                    Visibility="{Binding IsActive, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Grid>

                <!-- Logs tab -->
                <Grid Visibility="{Binding SelectedTab, Converter={StaticResource StringEqualityToVisibilityConverter}, ConverterParameter='Logs'}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Actions -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Content="Clear Logs"
                                Command="{Binding ClearLogsCommand}"
                                Width="100"
                                Height="30"
                                Margin="0,0,10,0"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FFFF3232"
                                BorderThickness="1"/>

                        <Button Content="Export Logs"
                                Command="{Binding ExportLogsCommand}"
                                Width="100"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"/>
                    </StackPanel>

                    <!-- Logs list -->
                    <DataGrid Grid.Row="1"
                              ItemsSource="{Binding LogEntries}"
                              AutoGenerateColumns="False"
                              Background="#FF050A0F"
                              BorderBrush="#FF00C8FF"
                              BorderThickness="1"
                              RowBackground="#FF0A141E"
                              AlternatingRowBackground="#FF050A0F"
                              HeadersVisibility="Column"
                              GridLinesVisibility="Horizontal"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Timestamp" Binding="{Binding Timestamp}" Width="150"/>
                            <DataGridTextColumn Header="Level" Binding="{Binding Level}" Width="80"/>
                            <DataGridTextColumn Header="Source" Binding="{Binding Source}" Width="100"/>
                            <DataGridTextColumn Header="Message" Binding="{Binding Message}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>

                <!-- System tab -->
                <Grid Visibility="{Binding SelectedTab, Converter={StaticResource StringEqualityToVisibilityConverter}, ConverterParameter='System'}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- System metrics -->
                    <DataGrid Grid.Row="1"
                              ItemsSource="{Binding SystemMetrics}"
                              AutoGenerateColumns="False"
                              Background="#FF050A0F"
                              BorderBrush="#FF00C8FF"
                              BorderThickness="1"
                              RowBackground="#FF0A141E"
                              AlternatingRowBackground="#FF050A0F"
                              HeadersVisibility="Column"
                              GridLinesVisibility="Horizontal"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Metric" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="Value" Binding="{Binding Value}" Width="150"/>
                            <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>

                <!-- Configuration tab -->
                <Grid Visibility="{Binding SelectedTab, Converter={StaticResource StringEqualityToVisibilityConverter}, ConverterParameter='Configuration'}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Actions -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Content="Save Config"
                                Command="{Binding SaveConfigCommand}"
                                Width="100"
                                Height="30"
                                Margin="0,0,10,0"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"/>

                        <Button Content="Reset Defaults"
                                Command="{Binding ResetConfigCommand}"
                                Width="120"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FFFF3232"
                                BorderThickness="1"/>
                    </StackPanel>

                    <!-- Configuration settings -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!-- System settings -->
                            <Border Background="#FF050A0F"
                                    BorderThickness="1"
                                    BorderBrush="#FF00C8FF"
                                    Padding="15"
                                    Margin="0,0,0,20">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <TextBlock Text="System Settings"
                                               FontFamily="Consolas"
                                               FontSize="16"
                                               FontWeight="Bold"
                                               Foreground="#FF00C8FF"
                                               Margin="0,0,0,15"/>

                                    <!-- Auto-start with Windows -->
                                    <Grid Grid.Row="1" Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="200"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Text="Auto-start with Windows:"
                                                   FontFamily="Consolas"
                                                   FontSize="12"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>

                                        <CheckBox Grid.Column="1"
                                                  IsChecked="{Binding AutoStartWithWindows}"
                                                  VerticalAlignment="Center"/>
                                    </Grid>

                                    <!-- Check for updates -->
                                    <Grid Grid.Row="2" Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="200"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Text="Check for updates:"
                                                   FontFamily="Consolas"
                                                   FontSize="12"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>

                                        <ComboBox Grid.Column="1"
                                                  SelectedIndex="{Binding UpdateCheckInterval}"
                                                  Width="150"
                                                  HorizontalAlignment="Left"
                                                  Background="#FF0A141E"
                                                  Foreground="White"
                                                  BorderBrush="#FF00C8FF"
                                                  BorderThickness="1">
                                            <ComboBoxItem Content="Never"/>
                                            <ComboBoxItem Content="Daily"/>
                                            <ComboBoxItem Content="Weekly"/>
                                            <ComboBoxItem Content="Monthly"/>
                                        </ComboBox>
                                    </Grid>

                                    <!-- Update server URL -->
                                    <Grid Grid.Row="3">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="200"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Text="Update server URL:"
                                                   FontFamily="Consolas"
                                                   FontSize="12"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>

                                        <TextBox Grid.Column="1"
                                                 Text="{Binding UpdateServerUrl}"
                                                 Background="#FF0A141E"
                                                 Foreground="White"
                                                 BorderBrush="#FF00C8FF"
                                                 BorderThickness="1"
                                                 Height="30"
                                                 Padding="5,0"
                                                 VerticalContentAlignment="Center"/>
                                    </Grid>
                                </Grid>
                            </Border>

                            <!-- Security settings -->
                            <Border Background="#FF050A0F"
                                    BorderThickness="1"
                                    BorderBrush="#FF00C8FF"
                                    Padding="15"
                                    Margin="0,0,0,20">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <TextBlock Text="Security Settings"
                                               FontFamily="Consolas"
                                               FontSize="16"
                                               FontWeight="Bold"
                                               Foreground="#FF00C8FF"
                                               Margin="0,0,0,15"/>

                                    <!-- Require login -->
                                    <Grid Grid.Row="1" Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="200"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Text="Require login:"
                                                   FontFamily="Consolas"
                                                   FontSize="12"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>

                                        <CheckBox Grid.Column="1"
                                                  IsChecked="{Binding RequireLogin}"
                                                  VerticalAlignment="Center"/>
                                    </Grid>

                                    <!-- Session timeout -->
                                    <Grid Grid.Row="2">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="200"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Text="Session timeout (min):"
                                                   FontFamily="Consolas"
                                                   FontSize="12"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>

                                        <TextBox Grid.Column="1"
                                                 Text="{Binding SessionTimeout}"
                                                 Background="#FF0A141E"
                                                 Foreground="White"
                                                 BorderBrush="#FF00C8FF"
                                                 BorderThickness="1"
                                                 Height="30"
                                                 Width="100"
                                                 HorizontalAlignment="Left"
                                                 Padding="5,0"
                                                 VerticalContentAlignment="Center"/>
                                    </Grid>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>

                <!-- Updates tab -->
                <Grid Visibility="{Binding SelectedTab, Converter={StaticResource StringEqualityToVisibilityConverter}, ConverterParameter='Updates'}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Actions -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Content="Check for Updates"
                                Command="{Binding CheckForUpdatesCommand}"
                                Width="150"
                                Height="30"
                                Margin="0,0,10,0"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"/>

                        <Button Content="Upload Update"
                                Command="{Binding UploadUpdateCommand}"
                                Width="120"
                                Height="30"
                                Margin="0,0,10,0"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"/>

                        <Button Content="Manage Versions"
                                Command="{Binding ManageVersionsCommand}"
                                Width="120"
                                Height="30"
                                Background="#FF001428"
                                Foreground="White"
                                BorderBrush="#FF00C8FF"
                                BorderThickness="1"/>
                    </StackPanel>

                    <!-- Update content -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="300"/>
                        </Grid.ColumnDefinitions>

                        <!-- Update history -->
                        <Border Grid.Column="0"
                                Background="#FF050A0F"
                                BorderThickness="1"
                                BorderBrush="#FF00C8FF"
                                Padding="15"
                                Margin="0,0,10,0">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Header -->
                                <TextBlock Text="Update History"
                                           FontFamily="Consolas"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="#FF00C8FF"
                                           Margin="0,0,0,15"/>

                                <!-- Update list -->
                                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                                    <StackPanel>
                                        <!-- Version 1.1.0 -->
                                        <Border Background="#FF0A141E"
                                                BorderThickness="0,0,0,1"
                                                BorderBrush="#FF004080"
                                                Padding="10"
                                                Margin="0,0,0,10">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- Version info -->
                                                <Grid Grid.Row="0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Text="Version 1.1.0"
                                                               FontFamily="Consolas"
                                                               FontSize="14"
                                                               FontWeight="Bold"
                                                               Foreground="White"/>

                                                    <TextBlock Grid.Column="2"
                                                               Text="Released: 2025-05-15"
                                                               FontFamily="Consolas"
                                                               FontSize="12"
                                                               Foreground="#FF808080"/>
                                                </Grid>

                                                <!-- Description -->
                                                <TextBlock Grid.Row="1"
                                                           Text="Major update with new features and performance improvements."
                                                           FontFamily="Segoe UI"
                                                           FontSize="12"
                                                           Foreground="#FFC0C0C0"
                                                           TextWrapping="Wrap"
                                                           Margin="0,5,0,5"/>

                                                <!-- Change log -->
                                                <Expander Grid.Row="2"
                                                          Header="Change Log"
                                                          Foreground="#FF00C8FF"
                                                          Background="Transparent"
                                                          BorderThickness="0">
                                                    <TextBlock Text="• Added new game profiles&#x0a;• Improved input delay reduction&#x0a;• Fixed controller detection issues&#x0a;• Added Discord integration&#x0a;• Performance optimizations"
                                                               FontFamily="Segoe UI"
                                                               FontSize="12"
                                                               Foreground="#FFC0C0C0"
                                                               TextWrapping="Wrap"
                                                               Margin="10,5,0,0"/>
                                                </Expander>
                                            </Grid>
                                        </Border>

                                        <!-- Version 1.0.5 -->
                                        <Border Background="#FF0A141E"
                                                BorderThickness="0,0,0,1"
                                                BorderBrush="#FF004080"
                                                Padding="10"
                                                Margin="0,0,0,10">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- Version info -->
                                                <Grid Grid.Row="0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Text="Version 1.0.5"
                                                               FontFamily="Consolas"
                                                               FontSize="14"
                                                               FontWeight="Bold"
                                                               Foreground="White"/>

                                                    <TextBlock Grid.Column="2"
                                                               Text="Released: 2025-04-20"
                                                               FontFamily="Consolas"
                                                               FontSize="12"
                                                               Foreground="#FF808080"/>
                                                </Grid>

                                                <!-- Description -->
                                                <TextBlock Grid.Row="1"
                                                           Text="Maintenance update with bug fixes and minor improvements."
                                                           FontFamily="Segoe UI"
                                                           FontSize="12"
                                                           Foreground="#FFC0C0C0"
                                                           TextWrapping="Wrap"
                                                           Margin="0,5,0,5"/>

                                                <!-- Change log -->
                                                <Expander Grid.Row="2"
                                                          Header="Change Log"
                                                          Foreground="#FF00C8FF"
                                                          Background="Transparent"
                                                          BorderThickness="0">
                                                    <TextBlock Text="• Fixed startup crash on some systems&#x0a;• Improved UI responsiveness&#x0a;• Added new system tweaks&#x0a;• Updated game profiles"
                                                               FontFamily="Segoe UI"
                                                               FontSize="12"
                                                               Foreground="#FFC0C0C0"
                                                               TextWrapping="Wrap"
                                                               Margin="10,5,0,0"/>
                                                </Expander>
                                            </Grid>
                                        </Border>

                                        <!-- Version 1.0.0 -->
                                        <Border Background="#FF0A141E"
                                                BorderThickness="0,0,0,1"
                                                BorderBrush="#FF004080"
                                                Padding="10"
                                                Margin="0,0,0,10">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- Version info -->
                                                <Grid Grid.Row="0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Text="Version 1.0.0"
                                                               FontFamily="Consolas"
                                                               FontSize="14"
                                                               FontWeight="Bold"
                                                               Foreground="White"/>

                                                    <TextBlock Grid.Column="2"
                                                               Text="Released: 2025-03-01"
                                                               FontFamily="Consolas"
                                                               FontSize="12"
                                                               Foreground="#FF808080"/>
                                                </Grid>

                                                <!-- Description -->
                                                <TextBlock Grid.Row="1"
                                                           Text="Initial release of The Circle Utility."
                                                           FontFamily="Segoe UI"
                                                           FontSize="12"
                                                           Foreground="#FFC0C0C0"
                                                           TextWrapping="Wrap"
                                                           Margin="0,5,0,5"/>

                                                <!-- Change log -->
                                                <Expander Grid.Row="2"
                                                          Header="Change Log"
                                                          Foreground="#FF00C8FF"
                                                          Background="Transparent"
                                                          BorderThickness="0">
                                                    <TextBlock Text="• Initial release&#x0a;• System optimization features&#x0a;• Game profiles for popular titles&#x0a;• Input delay reduction&#x0a;• Debloat functionality"
                                                               FontFamily="Segoe UI"
                                                               FontSize="12"
                                                               Foreground="#FFC0C0C0"
                                                               TextWrapping="Wrap"
                                                               Margin="10,5,0,0"/>
                                                </Expander>
                                            </Grid>
                                        </Border>
                                    </StackPanel>
                                </ScrollViewer>
                            </Grid>
                        </Border>

                        <!-- Update status -->
                        <Border Grid.Column="1"
                                Background="#FF050A0F"
                                BorderThickness="1"
                                BorderBrush="#FF00C8FF"
                                Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Header -->
                                <TextBlock Text="Update Status"
                                           FontFamily="Consolas"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="#FF00C8FF"
                                           Margin="0,0,0,15"/>

                                <!-- Current version -->
                                <Grid Grid.Row="1" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Text="Current version:"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               Foreground="#FF808080"
                                               VerticalAlignment="Center"/>

                                    <TextBlock Grid.Column="1"
                                               Text="1.1.0"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                                </Grid>

                                <!-- Latest version -->
                                <Grid Grid.Row="2" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Text="Latest version:"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               Foreground="#FF808080"
                                               VerticalAlignment="Center"/>

                                    <TextBlock Grid.Column="1"
                                               Text="1.1.0"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                                </Grid>

                                <!-- Last checked -->
                                <Grid Grid.Row="3" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Text="Last checked:"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               Foreground="#FF808080"
                                               VerticalAlignment="Center"/>

                                    <TextBlock Grid.Column="1"
                                               Text="2025-05-17 10:30 AM"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                                </Grid>

                                <!-- Auto-update settings -->
                                <Grid Grid.Row="4" Margin="0,0,0,20">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <TextBlock Text="Auto-Update Settings"
                                               FontFamily="Consolas"
                                               FontSize="14"
                                               FontWeight="Bold"
                                               Foreground="#FF00C8FF"
                                               Margin="0,0,0,10"/>

                                    <!-- Enable auto-update -->
                                    <Grid Grid.Row="1" Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Text="Auto-update:"
                                                   FontFamily="Consolas"
                                                   FontSize="12"
                                                   Foreground="#FF808080"
                                                   VerticalAlignment="Center"/>

                                        <CheckBox Grid.Column="1"
                                                  IsChecked="{Binding AutoUpdate}"
                                                  Content="Enable automatic updates"
                                                  Foreground="White"
                                                  VerticalAlignment="Center"/>
                                    </Grid>

                                    <!-- Update check interval -->
                                    <Grid Grid.Row="2">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Text="Check interval:"
                                                   FontFamily="Consolas"
                                                   FontSize="12"
                                                   Foreground="#FF808080"
                                                   VerticalAlignment="Center"/>

                                        <ComboBox Grid.Column="1"
                                                  SelectedIndex="{Binding UpdateCheckInterval}"
                                                  Width="150"
                                                  HorizontalAlignment="Left"
                                                  Background="#FF0A141E"
                                                  Foreground="White"
                                                  BorderBrush="#FF00C8FF"
                                                  BorderThickness="1">
                                            <ComboBoxItem Content="Never"/>
                                            <ComboBoxItem Content="Daily"/>
                                            <ComboBoxItem Content="Weekly"/>
                                            <ComboBoxItem Content="Monthly"/>
                                        </ComboBox>
                                    </Grid>
                                </Grid>

                                <!-- Actions -->
                                <StackPanel Grid.Row="5" VerticalAlignment="Bottom">
                                    <Button Content="Create New Release"
                                            Command="{Binding CreateReleaseCommand}"
                                            Height="40"
                                            Margin="0,0,0,10"
                                            Background="#FF001428"
                                            Foreground="White"
                                            BorderBrush="#FF00C8FF"
                                            BorderThickness="1"/>

                                    <Button Content="View Release Notes"
                                            Command="{Binding ViewReleaseNotesCommand}"
                                            Height="40"
                                            Background="#FF001428"
                                            Foreground="White"
                                            BorderBrush="#FF00C8FF"
                                            BorderThickness="1"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>

        <!-- Footer -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <TextBlock Text="Refresh Interval: "
                       FontFamily="Consolas"
                       FontSize="12"
                       Foreground="#FF808080"
                       VerticalAlignment="Center"/>

            <ComboBox SelectedIndex="{Binding RefreshIntervalIndex}"
                      Width="100"
                      Height="25"
                      Margin="5,0,10,0"
                      Background="#FF0A141E"
                      Foreground="#FF00C8FF"
                      BorderBrush="#FF00C8FF"
                      BorderThickness="1">
                <ComboBox.Resources>
                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#FF004080"/>
                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightTextBrushKey}" Color="#FF00C8FF"/>
                </ComboBox.Resources>
                <ComboBoxItem Content="Off"/>
                <ComboBoxItem Content="5 seconds"/>
                <ComboBoxItem Content="10 seconds"/>
                <ComboBoxItem Content="30 seconds"/>
                <ComboBoxItem Content="1 minute"/>
            </ComboBox>

            <Button Content="Refresh"
                    Command="{Binding RefreshDataCommand}"
                    Width="80"
                    Height="25"
                    Background="#FF001428"
                    Foreground="White"
                    BorderBrush="#FF00C8FF"
                    BorderThickness="1"/>
        </StackPanel>
    </Grid>
</UserControl>



