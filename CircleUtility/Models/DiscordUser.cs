using System;
using System.ComponentModel;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents a Discord user account
    /// </summary>
    public class DiscordUser : INotifyPropertyChanged
    {
        private string _username = string.Empty;
        private string _password = string.Empty;
        private string _displayName = string.Empty;
        private string _role = "User";
        private bool _isActive = true;
        private string _discordId = string.Empty;
        private DateTime _joinDate = DateTime.Now;
        private DateTime? _lastLogin;
        private string _email = string.Empty;
        private string _avatarUrl = string.Empty;

        /// <summary>
        /// Gets or sets the username
        /// </summary>
        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged(nameof(Username));
            }
        }

        /// <summary>
        /// Gets or sets the password
        /// </summary>
        public string Password
        {
            get => _password;
            set
            {
                _password = value;
                OnPropertyChanged(nameof(Password));
            }
        }

        /// <summary>
        /// Gets or sets the display name
        /// </summary>
        public string DisplayName
        {
            get => _displayName;
            set
            {
                _displayName = value;
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        /// <summary>
        /// Gets or sets the user role (User, Admin, Moderator)
        /// </summary>
        public string Role
        {
            get => _role;
            set
            {
                _role = value;
                OnPropertyChanged(nameof(Role));
                OnPropertyChanged(nameof(IsAdmin));
            }
        }

        /// <summary>
        /// Gets whether the user is an admin
        /// </summary>
        public bool IsAdmin => Role?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true;

        /// <summary>
        /// Gets or sets whether the user account is active
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        /// <summary>
        /// Gets or sets the Discord ID
        /// </summary>
        public string DiscordId
        {
            get => _discordId;
            set
            {
                _discordId = value;
                OnPropertyChanged(nameof(DiscordId));
            }
        }

        /// <summary>
        /// Gets or sets the join date
        /// </summary>
        public DateTime JoinDate
        {
            get => _joinDate;
            set
            {
                _joinDate = value;
                OnPropertyChanged(nameof(JoinDate));
            }
        }

        /// <summary>
        /// Gets or sets the last login date
        /// </summary>
        public DateTime? LastLogin
        {
            get => _lastLogin;
            set
            {
                _lastLogin = value;
                OnPropertyChanged(nameof(LastLogin));
                OnPropertyChanged(nameof(LastLoginFormatted));
            }
        }

        /// <summary>
        /// Gets the formatted last login date
        /// </summary>
        public string LastLoginFormatted => LastLogin?.ToString("yyyy-MM-dd HH:mm") ?? "Never";

        /// <summary>
        /// Gets or sets the email address
        /// </summary>
        public string Email
        {
            get => _email;
            set
            {
                _email = value;
                OnPropertyChanged(nameof(Email));
            }
        }

        /// <summary>
        /// Gets or sets the avatar URL
        /// </summary>
        public string AvatarUrl
        {
            get => _avatarUrl;
            set
            {
                _avatarUrl = value;
                OnPropertyChanged(nameof(AvatarUrl));
            }
        }

        /// <summary>
        /// Gets the role color for UI display
        /// </summary>
        public string RoleColor => Role switch
        {
            "Admin" => "#FF00C8FF",
            "Moderator" => "#FFFF8C00",
            "User" => "#FF00FF00",
            _ => "#FFFFFF"
        };

        /// <summary>
        /// Gets the status color for UI display
        /// </summary>
        public string StatusColor => IsActive ? "#FF00FF00" : "#FFFF0000";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Creates a copy of the user
        /// </summary>
        public DiscordUser Clone()
        {
            return new DiscordUser
            {
                Username = Username,
                Password = Password,
                DisplayName = DisplayName,
                Role = Role,
                IsActive = IsActive,
                DiscordId = DiscordId,
                JoinDate = JoinDate,
                LastLogin = LastLogin,
                Email = Email,
                AvatarUrl = AvatarUrl
            };
        }

        public override string ToString()
        {
            return $"{Username} ({Role}) - {(IsActive ? "Active" : "Inactive")}";
        }
    }
}
