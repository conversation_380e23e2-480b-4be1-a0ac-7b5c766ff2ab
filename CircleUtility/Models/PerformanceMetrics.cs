using System;

namespace CircleUtility.Models
{
    /// <summary>
    /// Represents comprehensive performance metrics
    /// </summary>
    public class PerformanceMetrics
    {
        /// <summary>
        /// Gets or sets the CPU usage percentage
        /// </summary>
        public float CpuUsage { get; set; }

        /// <summary>
        /// Gets or sets the memory usage percentage
        /// </summary>
        public float MemoryUsage { get; set; }

        /// <summary>
        /// Gets or sets the RAM usage percentage (alias for MemoryUsage)
        /// </summary>
        public float RamUsage 
        { 
            get => MemoryUsage; 
            set => MemoryUsage = value; 
        }

        /// <summary>
        /// Gets or sets the disk usage percentage
        /// </summary>
        public float DiskUsage { get; set; }

        /// <summary>
        /// Gets or sets the network usage
        /// </summary>
        public float NetworkUsage { get; set; }

        /// <summary>
        /// Gets or sets the GPU usage percentage
        /// </summary>
        public float GpuUsage { get; set; }

        /// <summary>
        /// Gets or sets the CPU temperature in Celsius
        /// </summary>
        public float CpuTemperature { get; set; }

        /// <summary>
        /// Gets or sets the GPU temperature in Celsius
        /// </summary>
        public float GpuTemperature { get; set; }

        /// <summary>
        /// Gets or sets the frames per second
        /// </summary>
        public float FPS { get; set; }

        /// <summary>
        /// Gets or sets the network download rate in MB/s
        /// </summary>
        public float NetworkDownload { get; set; }

        /// <summary>
        /// Gets or sets the network upload rate in MB/s
        /// </summary>
        public float NetworkUpload { get; set; }

        /// <summary>
        /// Gets or sets the disk read rate in MB/s
        /// </summary>
        public float DiskReadRate { get; set; }

        /// <summary>
        /// Gets or sets the disk write rate in MB/s
        /// </summary>
        public float DiskWriteRate { get; set; }

        /// <summary>
        /// Gets or sets the timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the frame time in milliseconds
        /// </summary>
        public float FrameTime { get; set; }

        /// <summary>
        /// Gets or sets the total available memory in GB
        /// </summary>
        public float TotalMemory { get; set; }

        /// <summary>
        /// Gets or sets the available memory in GB
        /// </summary>
        public float AvailableMemory { get; set; }

        /// <summary>
        /// Gets or sets the used memory in GB
        /// </summary>
        public float UsedMemory { get; set; }

        /// <summary>
        /// Initializes a new instance of the PerformanceMetrics class
        /// </summary>
        public PerformanceMetrics()
        {
            Timestamp = DateTime.Now;
        }
    }
}

