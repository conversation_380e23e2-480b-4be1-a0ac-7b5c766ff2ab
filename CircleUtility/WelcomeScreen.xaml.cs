using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using CircleUtility.Services;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtility
{
    public partial class WelcomeScreen : Window
    {
        private string[] loadingMessages = new string[]
        {
            "INITIALIZING CIRCLE UTILITY...",
            "AUTHENTICATING USER...",
            "LOADING HARDWARE SERVICES...",
            "SCANNING SYSTEM CONFIGURATION...",
            "OPTIMIZING PERFORMANCE SETTINGS...",
            "CONFIGURING SECURITY PROTOCOLS...",
            "PREPARING USER INTERFACE...",
            "FINALIZING SETUP...",
            "SYSTEM READY"
        };

        private int currentMessageIndex = 0;
        private DispatcherTimer messageTimer;
        private Random random = new Random();
        private bool isAuthenticated = false;

        public string Username { get; private set; }
        public bool IsLoginSuccessful { get; private set; }

        public WelcomeScreen()
        {
            InitializeComponent();

            // Username and password fields start empty

            // Start the pulse animation for the title
            Storyboard pulseAnimation = (Storyboard)FindResource("PulseAnimation");
            pulseAnimation.Begin();

            // Handle the access button click
            AccessButton.Click += AccessButton_Click;

            // Handle Enter key in password fields
            PasswordInput.KeyDown += (s, e) => 
            {
                if (e.Key == System.Windows.Input.Key.Enter)
                {
                    AccessButton_Click(AccessButton, new RoutedEventArgs());
                }
            };
            
            PasswordTextInput.KeyDown += (s, e) => 
            {
                if (e.Key == System.Windows.Input.Key.Enter)
                {
                    AccessButton_Click(AccessButton, new RoutedEventArgs());
                }
            };

            // Initially show login form
            StatusMessage.Text = "Please enter your credentials to access the system.";
            LoadingStatus.Text = "AWAITING LOGIN...";
        }

        private async void AccessButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Disable the button during authentication
                AccessButton.IsEnabled = false;
                AccessButton.Content = "AUTHENTICATING...";

                // Get credentials
                string username = UsernameInput.Text.Trim();
                string password = GetPasswordValue();

                // Validate input
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    ShowError("Please enter both username and password.");
                    return;
                }

                // Show authentication message
                StatusMessage.Text = "Authenticating user credentials...";
                LoadingStatus.Text = "VERIFYING...";

                // Perform simple authentication (simplified)
                bool authResult = await AuthenticateUser(username, password);

                if (authResult)
                {
                    // Authentication successful
                    isAuthenticated = true;
                    Username = username;
                    IsLoginSuccessful = true;

                    // Update UI
                    StatusMessage.Text = "Authentication successful! Loading system...";
                    AccessButton.Content = "LOADING...";

                    // Start loading sequence
                    await StartLoadingSequence();

                    // Close with success
                    DialogResult = true;
                    Close();
                }
                else
                {
                    // Authentication failed
                    ShowError("Invalid username or password. Please try again.");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Authentication error: {ex.Message}");
            }
            finally
            {
                // Re-enable button if still visible
                if (IsVisible)
                {
                    AccessButton.IsEnabled = true;
                    AccessButton.Content = "LOGIN & ACCESS";
                }
            }
        }

        private async Task<bool> AuthenticateUser(string username, string password)
        {
            try
            {
                // Use DiscordUserService for authentication
                var discordUserService = new EnhancedDiscordUserService();
                
                // You can add a Discord file URL here if needed
                // string discordFileUrl = "https://discord.com/api/webhooks/your-webhook-url/users.json";
                
                bool isAuthenticated = await discordUserService.AuthenticateUserAsync(username, password);
                
                if (isAuthenticated)
                {
                    // Update last login time
                    var user = await discordUserService.GetUserAsync(username);
                    if (user != null)
                    {
                        user.LastLogin = DateTime.Now;
                        // Save updated user info back to file
                        var users = await discordUserService.LoadUsersFromFileAsync();
                        // This will trigger a save to local file
                    }
                }
                
                return isAuthenticated;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Authentication error: {ex.Message}");
                return false;
            }
        }

        private async Task StartLoadingSequence()
        {
            // Start the loading bar animation
            Storyboard loadingAnimation = (Storyboard)FindResource("LoadingAnimation");
            loadingAnimation.Begin();

            // Setup the message timer
            messageTimer = new DispatcherTimer();
            messageTimer.Interval = TimeSpan.FromMilliseconds(600);
            messageTimer.Tick += MessageTimer_Tick;
            messageTimer.Start();

            // Wait for loading to complete
            await Task.Delay(4000);

            // Stop timer
            messageTimer?.Stop();

            // Set final status
            LoadingStatus.Text = loadingMessages[loadingMessages.Length - 1];
            StatusMessage.Text = "System ready. Launching main application...";
        }

        private void MessageTimer_Tick(object sender, EventArgs e)
        {
            // Update the loading message
            if (currentMessageIndex < loadingMessages.Length - 1)
            {
                currentMessageIndex++;
                LoadingStatus.Text = loadingMessages[currentMessageIndex];

                // Update the status message for some messages
                if (currentMessageIndex == 2)
                {
                    StatusMessage.Text = "Analyzing system configuration...";
                }
                else if (currentMessageIndex == 4)
                {
                    StatusMessage.Text = "Preparing optimization engine...";
                }
                else if (currentMessageIndex == 6)
                {
                    StatusMessage.Text = "Finalizing user interface...";
                }
            }
        }

        private void ShowError(string message)
        {
            StatusMessage.Text = message;
            StatusMessage.Foreground = new SolidColorBrush(Colors.Red);

            // Reset color after 3 seconds
            var timer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(3) };
            timer.Tick += (s, e) =>
            {
                StatusMessage.Foreground = new SolidColorBrush(Colors.White);
                StatusMessage.Text = "Please enter your credentials to access the system.";
                timer.Stop();
            };
            timer.Start();
        }


        /// <summary>
        /// Toggles password visibility
        /// </summary>
        private void PasswordToggleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (PasswordInput.Visibility == Visibility.Visible)
                {
                    // Switch to text mode (show password)
                    PasswordTextInput.Text = PasswordInput.Password;
                    PasswordInput.Visibility = Visibility.Collapsed;
                    PasswordTextInput.Visibility = Visibility.Visible;
                    PasswordToggleIcon.Text = "??"; // Hide icon
                    PasswordTextInput.Focus();
                }
                else
                {
                    // Switch to password mode (hide password)
                    PasswordInput.Password = PasswordTextInput.Text;
                    PasswordTextInput.Visibility = Visibility.Collapsed;
                    PasswordInput.Visibility = Visibility.Visible;
                    PasswordToggleIcon.Text = "???"; // Show icon
                    PasswordInput.Focus();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error toggling password visibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the current password value from either control
        /// </summary>
        private string GetPasswordValue()
        {
            if (PasswordInput.Visibility == Visibility.Visible)
            {
                return PasswordInput.Password;
            }
            else
            {
                return PasswordTextInput.Text;
            }
        }

                private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // Close the dialog with failure
            DialogResult = false;
            Close();
        }
    }
}
















