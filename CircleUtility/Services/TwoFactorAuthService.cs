using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Service for handling two-factor authentication
    /// </summary>
    public class TwoFactorAuthService
    {
        private static TwoFactorAuthService _instance;
        private static readonly object _lock = new object();
        private readonly LoggingService _logger;
        // Security service removed
        private readonly Dictionary<string, string> _pendingCodes;
        private readonly Random _random;

        /// <summary>
        /// Gets the singleton instance of the TwoFactorAuthService
        /// </summary>
        public static TwoFactorAuthService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new TwoFactorAuthService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Initializes a new instance of the TwoFactorAuthService class
        /// </summary>
        private TwoFactorAuthService()
        {
            _logger = LoggingService.Instance;
            // Security service removed
            _pendingCodes = new Dictionary<string, string>();
            _random = new Random();
        }

        /// <summary>
        /// Generates a verification code for the specified user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="twoFactorType">The two-factor authentication type</param>
        /// <returns>The verification code</returns>
        public string GenerateVerificationCode(string username, TwoFactorType twoFactorType)
        {
            try
            {
                // Generate a 6-digit code
                string code = _random.Next(100000, 999999).ToString();

                // Store the code
                if (_pendingCodes.ContainsKey(username))
                {
                    _pendingCodes[username] = code;
                }
                else
                {
                    _pendingCodes.Add(username, code);
                }

                // Log the action
                _logger.Log($"Generated verification code for user: {username}", LogLevel.INFO);

                // Return the code
                return code;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error generating verification code: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }

        /// <summary>
        /// Sends a verification code to the user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="code">The verification code</param>
        /// <param name="twoFactorType">The two-factor authentication type</param>
        /// <returns>True if the code was sent successfully, false otherwise</returns>
        public async Task<bool> SendVerificationCodeAsync(string username, string code, TwoFactorType twoFactorType)
        {
            try
            {
                // Get the user
                object user = null; // Security service removedGetUser(username);
                if (user == null)
                {
                    _logger.Log($"Error sending verification code: User not found: {username}", LogLevel.ERROR);
                    return false;
                }

                // Send the code based on the two-factor type
                switch (twoFactorType)
                {
                    case TwoFactorType.Email:
                        // In a real application, this would send an email
                        // For this demo, we'll just log it
                        _logger.Log($"Sending verification code to email: {user.Email}", LogLevel.INFO);
                        _logger.Log($"Verification code: {code}", LogLevel.INFO);
                        break;

                    case TwoFactorType.SMS:
                        // In a real application, this would send an SMS
                        // For this demo, we'll just log it
                        _logger.Log($"Sending verification code to phone: {user.PhoneNumber}", LogLevel.INFO);
                        _logger.Log($"Verification code: {code}", LogLevel.INFO);
                        break;

                    case TwoFactorType.Authenticator:
                        // For authenticator apps, we don't need to send anything
                        // The code is generated by the app
                        _logger.Log($"Authenticator app should be used for verification", LogLevel.INFO);
                        break;

                    default:
                        _logger.Log($"Unsupported two-factor type: {twoFactorType}", LogLevel.ERROR);
                        return false;
                }

                // Simulate network delay
                await Task.Delay(1000);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error sending verification code: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Verifies a verification code
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="code">The verification code</param>
        /// <returns>True if the code is valid, false otherwise</returns>
        public bool VerifyCode(string username, string code)
        {
            try
            {
                // Check if the user has a pending code
                if (!_pendingCodes.TryGetValue(username, out string storedCode))
                {
                    _logger.Log($"Error verifying code: No pending code for user: {username}", LogLevel.ERROR);
                    return false;
                }

                // Verify the code
                bool isValid = storedCode == code;

                // Remove the code if it's valid
                if (isValid)
                {
                    _pendingCodes.Remove(username);
                    _logger.Log($"Verification code verified for user: {username}", LogLevel.INFO);
                }
                else
                {
                    _logger.Log($"Invalid verification code for user: {username}", LogLevel.WARNING);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error verifying code: {ex.Message}", LogLevel.ERROR);
                return false;
            }
        }

        /// <summary>
        /// Generates recovery codes for the specified user
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="count">The number of recovery codes to generate</param>
        /// <returns>The recovery codes</returns>
        public string[] GenerateRecoveryCodes(string username, int count = 10)
        {
            try
            {
                // Generate recovery codes
                string[] recoveryCodes = new string[count];
                for (int i = 0; i < count; i++)
                {
                    // Generate a random 10-character code
                    byte[] buffer = new byte[5];
                    using (var rng = RandomNumberGenerator.Create())
                    {
                        rng.GetBytes(buffer);
                    }
                    recoveryCodes[i] = Convert.ToBase64String(buffer).Substring(0, 10);
                }

                // Log the action
                _logger.Log($"Generated {count} recovery codes for user: {username}", LogLevel.INFO);

                // Return the codes
                return recoveryCodes;
            }
            catch (Exception ex)
            {
                _logger.Log($"Error generating recovery codes: {ex.Message}", LogLevel.ERROR);
                return null;
            }
        }
    }
}



