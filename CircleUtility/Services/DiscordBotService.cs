using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Timers;
using System.Text;
using CircleUtility.Models;

namespace CircleUtility.Services
{
    /// <summary>
    /// Discord Bot Service for reading messages and processing commands
    /// Note: This is a simplified implementation. For full Discord Bot functionality,
    /// you would need Discord.NET library and proper bot token
    /// </summary>
    public class DiscordBotService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _commandsWebhookUrl;
        private readonly EnhancedDiscordUserService _userService;
        private Timer _messageCheckTimer;
        private readonly string _lastMessageFile;
        private DateTime _lastCheckedTime;

        public DiscordBotService(EnhancedDiscordUserService userService)
        {
            _httpClient = new HttpClient();
            _userService = userService;
            _commandsWebhookUrl = "https://discord.com/api/webhooks/1373372270596329584/iu4SrIROsxYYZNB66gpmuRiwovhX1uNf7lS-s21szYkmAamf2ppJl9wdghVkzc50CIGa";
            
            // File to track last checked message
            var documentsFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "CircleUtility");
            _lastMessageFile = Path.Combine(documentsFolder, "last_discord_check.txt");
            
            _lastCheckedTime = LoadLastCheckedTime();
            
            StartMessageMonitoring();
            
            Console.WriteLine("🤖 Discord Bot Service initialized");
        }

        /// <summary>
        /// Starts monitoring for Discord messages every 30 seconds
        /// </summary>
        private void StartMessageMonitoring()
        {
            try
            {
                _messageCheckTimer = new Timer(30 * 1000); // 30 seconds
                _messageCheckTimer.Elapsed += OnMessageCheckTimer;
                _messageCheckTimer.AutoReset = true;
                _messageCheckTimer.Start();

                Console.WriteLine("👁️ Discord message monitoring started (30-second intervals)");
                
                // Send startup notification
                _ = Task.Run(() => SendNotification("🚀 Circle Utility Discord Bot is now online and monitoring for .addprofile commands!"));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error starting message monitoring: {ex.Message}");
            }
        }

        /// <summary>
        /// Timer event handler
        /// </summary>
        private async void OnMessageCheckTimer(object sender, ElapsedEventArgs e)
        {
            await CheckForCommands();
        }

        /// <summary>
        /// Checks for new .addprofile commands
        /// Note: This is a simulation since we can't read Discord messages with webhooks
        /// In a real implementation, you'd use Discord Bot API
        /// </summary>
        private async Task CheckForCommands()
        {
            try
            {
                Console.WriteLine("🔍 Checking for Discord commands...");

                // Simulate checking for commands
                // In a real implementation, you would:
                // 1. Use Discord Bot API to read messages from the channel
                // 2. Filter messages since last check time
                // 3. Process .addprofile commands

                // For demonstration, let's create a sample command file that users can create
                await CheckForCommandFile();
                
                // Update last checked time
                _lastCheckedTime = DateTime.Now;
                await SaveLastCheckedTime();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error checking for commands: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks for a command file that users can create to simulate Discord commands
        /// </summary>
        private async Task CheckForCommandFile()
        {
            try
            {
                var documentsFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "CircleUtility");
                var commandFile = Path.Combine(documentsFolder, "discord_commands.txt");

                if (File.Exists(commandFile))
                {
                    Console.WriteLine("📝 Found Discord commands file, processing...");
                    
                    var commands = await File.ReadAllLinesAsync(commandFile);
                    
                    foreach (var command in commands)
                    {
                        if (!string.IsNullOrWhiteSpace(command))
                        {
                            await ProcessCommand(command.Trim());
                        }
                    }

                    // Archive the processed commands
                    var archiveFile = Path.Combine(documentsFolder, $"discord_commands_processed_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                    File.Move(commandFile, archiveFile);
                    
                    Console.WriteLine($"✅ Commands processed and archived to: {archiveFile}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error checking command file: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes a Discord command
        /// </summary>
        private async Task ProcessCommand(string command)
        {
            try
            {
                Console.WriteLine($"⚡ Processing command: {command}");

                if (command.StartsWith(".addprofile", StringComparison.OrdinalIgnoreCase))
                {
                    await ProcessAddProfileCommand(command);
                }
                else if (command.StartsWith(".listusers", StringComparison.OrdinalIgnoreCase))
                {
                    await ProcessListUsersCommand();
                }
                else if (command.StartsWith(".help", StringComparison.OrdinalIgnoreCase))
                {
                    await ProcessHelpCommand();
                }
                else
                {
                    await SendResponse($"❓ Unknown command: {command}. Type .help for available commands.");
                }
            }
            catch (Exception ex)
            {
                await SendResponse($"❌ Error processing command: {ex.Message}");
                Console.WriteLine($"🚨 Error processing command: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes .addprofile command
        /// Format: .addprofile username password displayname [role]
        /// </summary>
        private async Task ProcessAddProfileCommand(string command)
        {
            try
            {
                var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                
                if (parts.Length < 4)
                {
                    await SendResponse("❌ **Invalid format!**\n📝 **Usage:** `.addprofile username password displayname [role]`\n🔹 **Role:** `user` or `admin` (default: user)");
                    return;
                }

                string username = parts[1];
                string password = parts[2];
                string displayName = parts[3];
                string role = parts.Length > 4 ? parts[4] : "User";

                // Validate role
                if (!role.Equals("User", StringComparison.OrdinalIgnoreCase) && 
                    !role.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                {
                    role = "User";
                }

                // Create new user
                var newUser = new DiscordUser
                {
                    Username = username,
                    Password = password,
                    DisplayName = displayName,
                    Role = role,
                    IsActive = true,
                    DiscordId = $"discord_{DateTime.Now.Ticks}",
                    JoinDate = DateTime.Now,
                    Email = $"{username}@discord.local"
                };

                // Add user
                bool success = await _userService.AddUserAsync(newUser);
                
                if (success)
                {
                    await SendResponse($"✅ **User Added Successfully!**\n👤 **Username:** `{username}`\n🏷️ **Role:** `{role}`\n📝 **Display Name:** `{displayName}`\n📅 **Added:** {DateTime.Now:yyyy-MM-dd HH:mm}");
                    Console.WriteLine($"✅ Added user via Discord: {username} ({role})");
                }
                else
                {
                    await SendResponse($"❌ **Failed to add user:** `{username}`\n⚠️ User may already exist or invalid data provided.");
                }
            }
            catch (Exception ex)
            {
                await SendResponse($"❌ **Error:** {ex.Message}");
                Console.WriteLine($"🚨 Error in .addprofile: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes .listusers command
        /// </summary>
        private async Task ProcessListUsersCommand()
        {
            try
            {
                var users = await _userService.LoadUsersFromFileAsync(forceRefresh: true);
                
                var response = new StringBuilder();
                response.AppendLine("📋 **Current Users:**");
                response.AppendLine($"👥 **Total:** {users.Count} users");
                response.AppendLine($"🔹 **Regular:** {users.Count(u => !u.IsAdmin)}");
                response.AppendLine($"🔸 **Admin:** {users.Count(u => u.IsAdmin)}");
                response.AppendLine();

                response.AppendLine("**👑 Admin Users:**");
                foreach (var admin in users.Where(u => u.IsAdmin).Take(10))
                {
                    response.AppendLine($"• `{admin.Username}` - {admin.DisplayName}");
                }

                response.AppendLine();
                response.AppendLine("**👤 Regular Users:**");
                foreach (var user in users.Where(u => !u.IsAdmin).Take(10))
                {
                    response.AppendLine($"• `{user.Username}` - {user.DisplayName}");
                }

                if (users.Count > 20)
                {
                    response.AppendLine($"\n*... and {users.Count - 20} more users*");
                }

                await SendResponse(response.ToString());
            }
            catch (Exception ex)
            {
                await SendResponse($"❌ Error listing users: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes .help command
        /// </summary>
        private async Task ProcessHelpCommand()
        {
            var help = @"🤖 **Circle Utility Discord Bot Commands**

**📝 Available Commands:**
• `.addprofile username password displayname [role]` - Add new user
• `.listusers` - Show current users
• `.help` - Show this help message

**📋 Examples:**
• `.addprofile john mypass123 ""John Doe"" user`
• `.addprofile admin adminpass ""Administrator"" admin`

**📁 Alternative Method:**
Create a file: `Documents/CircleUtility/discord_commands.txt`
Add commands (one per line), the bot will process them automatically.

**⏰ Bot checks for commands every 30 seconds.**";

            await SendResponse(help);
        }

        /// <summary>
        /// Sends response to Discord
        /// </summary>
        private async Task SendResponse(string message)
        {
            try
            {
                var payload = new
                {
                    content = message
                };

                var json = JsonSerializer.Serialize(payload);
                var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_commandsWebhookUrl, httpContent);
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✅ Response sent to Discord");
                }
                else
                {
                    Console.WriteLine($"⚠️ Discord response failed: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error sending response: {ex.Message}");
            }
        }

        /// <summary>
        /// Sends notification to Discord
        /// </summary>
        private async Task SendNotification(string message)
        {
            await SendResponse(message);
        }

        /// <summary>
        /// Loads last checked time from file
        /// </summary>
        private DateTime LoadLastCheckedTime()
        {
            try
            {
                if (File.Exists(_lastMessageFile))
                {
                    var timeStr = File.ReadAllText(_lastMessageFile);
                    if (DateTime.TryParse(timeStr, out DateTime lastTime))
                    {
                        return lastTime;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error loading last checked time: {ex.Message}");
            }

            return DateTime.Now.AddMinutes(-5); // Default to 5 minutes ago
        }

        /// <summary>
        /// Saves last checked time to file
        /// </summary>
        private async Task SaveLastCheckedTime()
        {
            try
            {
                await File.WriteAllTextAsync(_lastMessageFile, _lastCheckedTime.ToString("O"));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨 Error saving last checked time: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _messageCheckTimer?.Stop();
            _messageCheckTimer?.Dispose();
            _httpClient?.Dispose();
            
            Console.WriteLine("🔄 Discord Bot Service disposed");
        }
    }
}
