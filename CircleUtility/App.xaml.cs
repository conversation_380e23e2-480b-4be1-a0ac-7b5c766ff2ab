using System;
using System.IO;
using System.Windows;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using CircleUtility.Services;
using CircleUtility.Interfaces;
using CircleUtility.Models;

namespace CircleUtility
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// Gets the service provider for dependency injection
        /// </summary>
        public static IServiceProvider ServiceProvider { get; private set; }

        private readonly string _crashFlagPath;

        /// <summary>
        /// Initializes a new instance of the App class
        /// </summary>
        public App()
        {
            _crashFlagPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "crash.flag");
        }

        /// <summary>
        /// Handles application startup
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The startup event args</param>
        private void Application_Startup(object sender, StartupEventArgs e)
        {
            try
            {
                LogMessage("Application starting up...");

                // Initialize services
                InitializeServices();

                // Show main window
                ShowMainWindow();

                LogMessage("Application startup completed successfully");
            }
            catch (Exception ex)
            {
                LogMessage($"Error in Application_Startup: {ex.Message}");
                MessageBox.Show($"Error starting application: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        /// <summary>
        /// Initializes all services
        /// </summary>
        private void InitializeServices()
        {
            try
            {
                LogMessage("Configuring dependency injection with standardized service registration");

                // Create a service collection
                var services = new ServiceCollection();

                // Register all services using standardized extension method
                LogMessage("Registering all CircleUtility services");
                services.AddCircleUtilityServices(
                    includeUIServices: true,
                    includeSecurityServices: true);

                // Build the service provider
                LogMessage("Building service provider");
                ServiceProvider = services.BuildServiceProvider();
                LogMessage("Service provider built");

                // Initialize all services through service manager
                LogMessage("Initializing all services through service manager");
                ServiceProvider.InitializeAllServices();
                LogMessage("All services initialized");
            }
            catch (Exception ex)
            {
                LogMessage($"Error initializing services: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Shows the main window
        /// </summary>
        private void ShowMainWindow()
        {
            try
            {
                LogMessage("Showing welcome/login screen");
                
                // Show welcome screen with login
                var welcomeScreen = new WelcomeScreen();
                bool? result = welcomeScreen.ShowDialog();
                
                if (result == true && welcomeScreen.IsLoginSuccessful)
                {
                    LogMessage($"Login successful for user: {welcomeScreen.Username}");
                    
                    // Create and show main window
                    var mainWindow = new MainWindow(
                        ServiceProvider.GetRequiredService<IHardwareDetectionService>(),
                        ServiceProvider.GetRequiredService<IHardwareOptimizationService>(),
                        ServiceProvider.GetRequiredService<IHardwareCompatibilityService>(),
                        ServiceProvider.GetRequiredService<IPerformanceMonitoringService>(),
                        ServiceProvider.GetRequiredService<IBenchmarkingService>(),
                        ServiceProvider.GetRequiredService<IHardwareRecommendationService>());
                    
                    mainWindow.Show();
                    LogMessage("Main window shown");
                    
                    // Initialize Discord Bot Service
                    try
                    {
                        var discordUserService = new EnhancedDiscordUserService();
                        var discordBotService = new DiscordBotService(discordUserService);
                        LogMessage("Discord Bot Service initialized");
                    }
                    catch (Exception discordEx)
                    {
                        LogMessage($"Discord Bot Service initialization failed: {discordEx.Message}");
                    }
                }
                else
                {
                    LogMessage("Login cancelled or failed. Shutting down application.");
                    Shutdown();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error showing main window: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Handles application exit
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="e">The exit event args</param>
        private void Application_Exit(object sender, ExitEventArgs e)
        {
            try
            {
                LogMessage("Application shutting down");

                // Dispose the service provider
                if (ServiceProvider is IDisposable disposableServiceProvider)
                {
                    disposableServiceProvider.Dispose();
                    LogMessage("Service provider disposed");
                }

                LogMessage("Application shutdown complete");
            }
            catch (Exception ex)
            {
                LogMessage($"Error during application shutdown: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs a message to console
        /// </summary>
        /// <param name="message">The message to log</param>
        private void LogMessage(string message)
        {
            try
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
            }
            catch
            {
                // Ignore logging errors during startup
            }
        }
    }
}





