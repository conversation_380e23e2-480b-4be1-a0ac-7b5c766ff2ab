[2025-05-18 18:25:39.774] Application constructor called
[2025-05-18 18:25:39.795] Application_Startup called
[2025-05-18 18:25:39.795] Creating main window
[2025-05-18 18:25:39.907] Main window created
[2025-05-18 18:25:39.907] Setting main window
[2025-05-18 18:25:39.908] Showing main window
[2025-05-18 18:25:40.026] Main window shown
[2025-05-18 18:26:01.928] Unhandled exception: 'AdminTabButtonActiveStyle' resource not found.
[2025-05-18 18:26:01.932] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:26:01.937] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:26:01.937] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:26:01.940] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:26:01.940] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:25.809] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:25.809] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:26.955] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:26.956] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:30.884] Unhandled exception: 'AdminTabButtonActiveStyle' resource not found.
[2025-05-18 18:28:30.884] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:31.979] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:31.979] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:32.406] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:32.406] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:32.740] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:32.741] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:33.085] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:33.086] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:38.711] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:38.711] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:39.733] Unhandled exception: 'AdminTabButtonActiveStyle' resource not found.
[2025-05-18 18:28:39.734] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:42.333] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:42.334] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:42.619] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:42.619] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-05-18 18:28:42.816] Unhandled exception: 'AdminTabButtonStyle' resource not found.
[2025-05-18 18:28:42.816] Stack trace:    at MS.Internal.Helper.FindResourceHelper.DoTryCatchWhen(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
