# Dev Mode Implementation for The Circle Utility

## Overview
I successfully implemented a Dev Mode feature for The Circle Utility that allows you to safely test the application without making actual system changes. This document explains how the Dev Mode works and how to toggle between Dev Mode and Live Mode. - Arsenal

## How Dev Mode Works

### 1. Mode Configuration
At the top of the `CircleUtilityWPF.ps1` file, there's a global variable that controls the mode:

```powershell
# Set to $true for development mode (no actual system changes)
# Set to $false for live mode (applies all changes)
$global:DevMode = $true
```

### 2. Mode Indicator
- The "DEV MODE" label appears in the top-right corner of both the login and main windows
- This label flashes with rainbow colors to make it highly visible
- The console displays a clear message indicating which mode is active
- The log file also records which mode is being used

### 3. System Change Prevention
We've implemented a `Should-ApplyChange` function that checks the current mode:

```powershell
function Should-ApplyChange {
    param (
        [string]$ActionName = "System change"
    )
    
    if ($global:DevMode) {
        Write-Host "DEV MODE: $ActionName prevented (no actual changes in Dev Mode)" -ForegroundColor Yellow
        return $false
    } else {
        return $true
    }
}
```

This function is called before making any system changes. If in Dev Mode, it prevents the change and displays a message. If in Live Mode, it allows the change to proceed.

### 4. User Feedback
- In Dev Mode, when a user tries to apply a system change, they receive a message explaining that the change would be applied in Live Mode but is being prevented in Dev Mode
- In Live Mode, system changes are applied normally with success messages

## How to Toggle Between Modes

To switch between Dev Mode and Live Mode:

1. Open the `CircleUtilityWPF.ps1` file in a text editor
2. Locate the Dev Mode configuration section at the top of the file
3. Change the value of `$global:DevMode` to:
   - `$true` for Dev Mode (no actual system changes)
   - `$false` for Live Mode (applies all changes)
4. Save the file
5. Run the utility

## Testing Results

We've tested both modes:

### Dev Mode Test
- Console clearly displays "Running CircleUtilityWPF.ps1 with WPF UI in DEV MODE"
- Warning message: "WARNING: System changes will be simulated but NOT actually applied"
- Log file shows warning level messages about Dev Mode
- "DEV MODE" label appears in the UI with rainbow animation

### Live Mode Test
- Console clearly displays "Running CircleUtilityWPF.ps1 with WPF UI in LIVE MODE"
- Note message: "NOTE: All system changes WILL be applied"
- Log file shows info level messages about Live Mode
- "DEV MODE" label is still present but system changes will be applied

## Recommendations

1. **Development and Testing**: Keep the utility in Dev Mode while developing and testing to prevent unintended system changes.

2. **Distribution**: Before distributing the utility to users, set it to Live Mode so that system changes are actually applied.

3. **Installer Creation**: When creating an installer, ensure that the version included has `$global:DevMode = $false` to provide full functionality to end users.

## Conclusion

The Dev Mode implementation provides a safe way to develop and test The Circle Utility without risking system changes. By simply changing one variable, you can switch between Dev Mode and Live Mode, making it easy to prepare the utility for distribution when ready.
