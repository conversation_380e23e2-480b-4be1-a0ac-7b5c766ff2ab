# Manually update WelcomeScreen.xaml to remove inline styles
$filePath = "CircleUtility\WelcomeScreen.xaml"
$content = Get-Content $filePath -Raw

# Remove the entire Window.Resources section
$content = $content -replace '    <!-- Resources for animations and styles -->\s*<Window\.Resources>.*?</Window\.Resources>', '    <!-- Resources are now centralized in App.xaml -->'

Set-Content $filePath -Value $content

Write-Host "WelcomeScreen.xaml inline styles removed!"
