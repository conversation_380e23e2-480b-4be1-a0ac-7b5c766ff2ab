using System;
using System.Collections.Generic;
using System.Net;
using System.Windows;
using System.Windows.Input;
using CircleUtility.Models;
using CircleUtility.Services;

namespace CircleUtilityAdmin
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly SecurityService _securityService;
        private readonly LoggingService _logger;
        private readonly HardwareFingerprintService _hardwareFingerprintService;
        private readonly string _currentHardwareId;
        private readonly string _currentIpAddress;
        private readonly List<string> _authorizedHardwareIds;
        private readonly List<string> _authorizedIpAddresses;

        public MainWindow()
        {
            InitializeComponent();

            // Initialize services
            _logger = LoggingService.Instance;
            _securityService = SecurityService.Instance;
            _hardwareFingerprintService = HardwareFingerprintService.Instance;

            // Get current hardware ID and IP address
            _currentHardwareId = _hardwareFingerprintService.GetHardwareFingerprint();
            _currentIpAddress = GetLocalIPAddress();

            // Set hardware ID and IP address in UI
            HardwareIdBox.Text = _currentHardwareId;
            IpAddressBox.Text = _currentIpAddress;

            // Define authorized hardware IDs and IP addresses
            _authorizedHardwareIds = new List<string>
            {
                _currentHardwareId // Add your hardware ID by default
                // Add more hardware IDs if needed
            };

            _authorizedIpAddresses = new List<string>
            {
                _currentIpAddress, // Add your IP address by default
                "127.0.0.1",       // Localhost
                "::1"              // IPv6 localhost
                // Add more IP addresses if needed
            };

            // Check if user is authorized
            if (!IsAuthorized())
            {
                MessageBox.Show("You are not authorized to use this application.", "Access Denied", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown();
            }

            _logger.Log("Admin Account Manager started", LogLevel.INFO);
        }

        /// <summary>
        /// Checks if the current user is authorized to use the application
        /// </summary>
        /// <returns>True if authorized, false otherwise</returns>
        private bool IsAuthorized()
        {
            // Check hardware ID
            bool hardwareAuthorized = _authorizedHardwareIds.Contains(_currentHardwareId);

            // Check IP address
            bool ipAuthorized = _authorizedIpAddresses.Contains(_currentIpAddress);

            // User is authorized if either hardware ID or IP address is authorized
            return hardwareAuthorized || ipAuthorized;
        }

        /// <summary>
        /// Gets the local IP address
        /// </summary>
        /// <returns>The local IP address</returns>
        private string GetLocalIPAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// Handles the create account button click
        /// </summary>
        private void CreateAccountButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Reset messages
                ErrorMessage.Visibility = Visibility.Collapsed;
                SuccessMessage.Visibility = Visibility.Collapsed;

                // Get input values
                string username = UsernameBox.Text.Trim();
                string password = PasswordBox.Password;
                string confirmPassword = ConfirmPasswordBox.Password;
                bool isAdmin = AdminCheckBox.IsChecked ?? false;

                // Validate input
                if (string.IsNullOrWhiteSpace(username))
                {
                    ShowError("Please enter a username.");
                    return;
                }

                if (string.IsNullOrWhiteSpace(password))
                {
                    ShowError("Please enter a password.");
                    return;
                }

                if (password != confirmPassword)
                {
                    ShowError("Passwords do not match.");
                    return;
                }

                // Check if username already exists
                if (_securityService.UserExists(username))
                {
                    ShowError("Username already exists. Please choose a different username.");
                    return;
                }

                // Create the user
                int securityLevel = isAdmin ? 3 : 1;
                bool isCreated = _securityService.CreateUser(username, password, securityLevel, isAdmin);

                if (isCreated)
                {
                    // Log success
                    _logger.Log($"User created: {username}, Admin: {isAdmin}", LogLevel.SUCCESS);
                    
                    // Show success message
                    ShowSuccess($"User '{username}' created successfully.");
                    
                    // Clear input fields
                    UsernameBox.Text = "";
                    PasswordBox.Password = "";
                    ConfirmPasswordBox.Password = "";
                    AdminCheckBox.IsChecked = false;
                }
                else
                {
                    // Log error
                    _logger.Log($"Failed to create user: {username}", LogLevel.ERROR);
                    
                    // Show error message
                    ShowError("Failed to create user. Please try again.");
                }
            }
            catch (Exception ex)
            {
                _logger.Log($"Error creating user: {ex.Message}", LogLevel.ERROR);
                ShowError($"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows an error message
        /// </summary>
        private void ShowError(string message)
        {
            ErrorMessage.Text = message;
            ErrorMessage.Visibility = Visibility.Visible;
            SuccessMessage.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// Shows a success message
        /// </summary>
        private void ShowSuccess(string message)
        {
            SuccessMessage.Text = message;
            SuccessMessage.Visibility = Visibility.Visible;
            ErrorMessage.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// Handles the window mouse down event for dragging
        /// </summary>
        private void Window_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
            {
                DragMove();
            }
        }

        /// <summary>
        /// Handles the minimize button click
        /// </summary>
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// Handles the close button click
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
