# Final comprehensive fix for the remaining 3 errors
Write-Host "Final comprehensive fix for the remaining 3 errors..."

# 1. Fix PerformanceMonitoringService to return PerformanceMetrics instead of object
Write-Host "1. Fixing PerformanceMonitoringService GetCurrentMetrics return type..."
$perfPath = "CircleUtility\Services\PerformanceMonitoringService.cs"
$perfContent = Get-Content $perfPath -Raw
$perfContent = $perfContent -replace 'public PerformanceMetrics GetCurrentMetrics\(\)\s*\{\s*return new PerformanceMetrics \{ CpuUsage = 0\.0f, MemoryUsage = 0\.0f \};', 'public PerformanceMetrics GetCurrentMetrics()
        {
            return new PerformanceMetrics 
            { 
                CpuUsage = 0.0f, 
                MemoryUsage = 0.0f,
                GpuUsage = 0.0f,
                DiskUsage = 0.0f,
                NetworkUsage = 0.0f,
                CpuTemperature = 0.0f,
                GpuTemperature = 0.0f,
                FPS = 0.0f,
                NetworkDownload = 0.0f,
                NetworkUpload = 0.0f,
                DiskReadRate = 0.0f,
                DiskWriteRate = 0.0f
            };'
Set-Content $perfPath -Value $perfContent

# 2. Fix CompatibilityTestingService GetCurrentMetrics call
Write-Host "2. Fixing CompatibilityTestingService GetCurrentMetrics call..."
$compatTestPath = "CircleUtility\Services\CompatibilityTestingService.cs"
$compatTestContent = Get-Content $compatTestPath -Raw
$compatTestContent = $compatTestContent -replace 'var metricsObj = _performanceMonitoringService\.GetCurrentMetrics\(\);\s*var metrics = \(metricsObj as PerformanceMetrics\) \?\? new PerformanceMetrics\(\);', 'var metrics = _performanceMonitoringService.GetCurrentMetrics();'
Set-Content $compatTestPath -Value $compatTestContent

# 3. Fix HardwareCompatibilityMonitor GetCurrentMetrics call
Write-Host "3. Fixing HardwareCompatibilityMonitor GetCurrentMetrics call..."
$hardwareCompatMonitorPath = "CircleUtility\Services\HardwareCompatibilityMonitor.cs"
$hardwareCompatMonitorContent = Get-Content $hardwareCompatMonitorPath -Raw
$hardwareCompatMonitorContent = $hardwareCompatMonitorContent -replace 'var metricsObj = _performanceMonitoringService\.GetCurrentMetrics\(\);\s*var metrics = \(metricsObj as PerformanceMetrics\) \?\? new PerformanceMetrics\(\);', 'var metrics = _performanceMonitoringService.GetCurrentMetrics();'
Set-Content $hardwareCompatMonitorPath -Value $hardwareCompatMonitorContent

# 4. Fix ExceptionHandlingService switch case by completely rewriting the file section
Write-Host "4. Fixing ExceptionHandlingService switch case..."
$exceptionPath = "CircleUtility\Services\ExceptionHandlingService.cs"
$exceptionContent = Get-Content $exceptionPath -Raw

# Find and replace the entire problematic method
$methodStart = $exceptionContent.IndexOf("private ExceptionSeverity DetermineExceptionSeverity(Exception exception)")
$methodEnd = $exceptionContent.IndexOf("}", $methodStart) + 1

if ($methodStart -gt -1 -and $methodEnd -gt $methodStart) {
    $beforeMethod = $exceptionContent.Substring(0, $methodStart)
    $afterMethod = $exceptionContent.Substring($methodEnd)
    
    $newMethod = @'
private ExceptionSeverity DetermineExceptionSeverity(Exception exception)
        {
            if (exception is OutOfMemoryException || 
                exception is StackOverflowException || 
                exception is AccessViolationException || 
                exception is AppDomainUnloadedException)
            {
                return ExceptionSeverity.Critical;
            }
            
            if (exception is UnauthorizedAccessException || 
                exception is System.Security.SecurityException)
            {
                return ExceptionSeverity.High;
            }
            
            if (exception is InvalidOperationException invalidOp && invalidOp.Message.Contains("disposed"))
            {
                return ExceptionSeverity.High;
            }
            
            if (exception is ArgumentException || 
                exception is ArgumentNullException || 
                exception is InvalidOperationException || 
                exception is NotSupportedException)
            {
                return ExceptionSeverity.Medium;
            }
            
            if (exception is System.IO.FileNotFoundException || 
                exception is System.IO.DirectoryNotFoundException || 
                exception is System.Net.NetworkInformation.NetworkInformationException)
            {
                return ExceptionSeverity.Low;
            }
            
            return ExceptionSeverity.Medium;
        }
'@
    
    $newContent = $beforeMethod + $newMethod + $afterMethod
    Set-Content $exceptionPath -Value $newContent
}

Write-Host "Final comprehensive fix completed!"
