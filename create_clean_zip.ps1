# Create a clean ZIP excluding security files and build artifacts
Write-Host "Creating clean ZIP of CircleUtility (excluding security files)..."

$sourceDir = "C:\Users\<USER>\Desktop\TheCircleUtility"
$zipPath = "C:\Users\<USER>\Desktop\TheCircleUtility_Clean.zip"

# Remove existing ZIP if it exists
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
    Write-Host "Removed existing ZIP file"
}

# Create temporary directory for clean copy
$tempDir = "C:\Users\<USER>\Desktop\TheCircleUtility_Temp"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

Write-Host "Copying source files (excluding security and build files)..."

# Copy source files excluding problematic directories
robocopy $sourceDir $tempDir /E /XD "bin" "obj" "SecureConfig" "Security" ".vs" ".git" "packages" /XF "*.user" "*.suo" "*.cache" "*.log" "*.dat" "*.key" /NFL /NDL /NJH /NJS

# Create ZIP from clean copy
Write-Host "Creating ZIP file..."
Compress-Archive -Path "$tempDir\*" -DestinationPath $zipPath -Force

# Clean up temp directory
Remove-Item $tempDir -Recurse -Force

Write-Host "Clean ZIP created successfully: $zipPath"
Write-Host "Size: $((Get-Item $zipPath).Length / 1MB) MB"
