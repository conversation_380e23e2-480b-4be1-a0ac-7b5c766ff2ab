# Clean up MainWindow.xaml namespace declarations
$filePath = "CircleUtility\MainWindow.xaml"
$content = Get-Content $filePath -Raw

# Remove duplicate namespace declarations
$content = $content -replace 'xmlns:local="clr-namespace:CircleUtility\.Converters"\s*', ''
$content = $content -replace 'xmlns:helpers="clr-namespace:CircleUtility\.Helpers"\s*', ''

# Keep only the necessary namespaces
# viewmodels, views, and converters are the main ones needed

Set-Content $filePath -Value $content

Write-Host "MainWindow.xaml namespace declarations cleaned up!"
