# Delete security files that are causing ZIP access errors
Write-Host "Deleting security files that are causing ZIP access errors..."

$securityFiles = @(
    "CircleUtility\bin\Debug\net6.0-windows\SecureConfig\secure_key.dat",
    "CircleUtility\bin\Debug\net6.0-windows\Security\key.dat",
    "CircleUtility\bin\Release\net6.0-windows\SecureConfig\secure_key.dat",
    "CircleUtility\bin\Release\net6.0-windows\Security\key.dat",
    "CircleUtilityAdmin\bin\Debug\net6.0-windows\Security\key.dat"
)

$deletedCount = 0
$notFoundCount = 0

foreach ($file in $securityFiles) {
    if (Test-Path $file) {
        try {
            Remove-Item $file -Force
            Write-Host "✅ Deleted: $file"
            $deletedCount++
        }
        catch {
            Write-Host "❌ Failed to delete: $file - $($_.Exception.Message)"
        }
    }
    else {
        Write-Host "ℹ️  Not found: $file"
        $notFoundCount++
    }
}

Write-Host ""
Write-Host "Summary:"
Write-Host "✅ Files deleted: $deletedCount"
Write-Host "ℹ️  Files not found: $notFoundCount"
Write-Host ""
Write-Host "You can now create your ZIP file without access errors!"
