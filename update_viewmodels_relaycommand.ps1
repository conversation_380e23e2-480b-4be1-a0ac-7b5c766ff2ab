# Update ViewModels to use CircleUtility.Commands.RelayCommand instead of CircleUtility.Helpers.RelayCommand

$viewModelFiles = @(
    "CircleUtility\ViewModels\InputDelayViewModel.cs",
    "CircleUtility\ViewModels\DebloatViewModel.cs", 
    "CircleUtility\ViewModels\MainViewModel.cs",
    "CircleUtility\ViewModels\BenchmarkViewModel.cs",
    "CircleUtility\ViewModels\DiscordViewModel.cs",
    "CircleUtility\ViewModels\AdminViewModel.cs",
    "CircleUtility\ViewModels\RevertTweaksViewModel.cs",
    "CircleUtility\Controls\NotificationCenter.xaml.cs"
)

foreach ($file in $viewModelFiles) {
    if (Test-Path $file) {
        Write-Host "Updating $file..."
        $content = Get-Content $file -Raw
        
        # Replace the using statement
        $content = $content -replace 'using CircleUtility\.Helpers;', 'using CircleUtility.Commands;'
        
        Set-Content $file -Value $content
        Write-Host "  ✓ Updated $file"
    } else {
        Write-Host "  ⚠ File not found: $file"
    }
}

Write-Host "All ViewModels updated to use CircleUtility.Commands.RelayCommand!"
