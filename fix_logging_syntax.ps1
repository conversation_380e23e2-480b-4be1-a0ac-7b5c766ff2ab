# Fix LoggingService syntax error
$filePath = "CircleUtility\Services\LoggingService.cs"
$content = Get-Content $filePath -Raw

# Fix the missing closing brace after Flush<PERSON><PERSON><PERSON>uffer method
$content = $content -replace 'Console\.WriteLine\(\$"Error flushing log buffer: \{ex\.Message\}"\);\s*}\s*private async Task', 'Console.WriteLine($"Error flushing log buffer: {ex.Message}");
            }
        }

        private async Task'

Set-Content $filePath -Value $content

Write-Host "LoggingService syntax error fixed!"
