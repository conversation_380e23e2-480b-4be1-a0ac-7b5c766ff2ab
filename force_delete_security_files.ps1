# Force delete security files with elevated permissions
Write-Host "Force deleting security files with elevated permissions..."

$securityFiles = @(
    "CircleUtility\bin\Debug\net6.0-windows\SecureConfig\secure_key.dat",
    "CircleUtility\bin\Debug\net6.0-windows\Security\key.dat",
    "CircleUtility\bin\Release\net6.0-windows\SecureConfig\secure_key.dat",
    "CircleUtility\bin\Release\net6.0-windows\Security\key.dat",
    "CircleUtilityAdmin\bin\Debug\net6.0-windows\Security\key.dat"
)

foreach ($file in $securityFiles) {
    if (Test-Path $file) {
        try {
            # Try to take ownership and change permissions
            Write-Host "Attempting to take ownership of: $file"
            takeown /f "$file" /a 2>$null
            icacls "$file" /grant administrators:F 2>$null
            
            # Try to remove read-only attribute
            attrib -r "$file" 2>$null
            
            # Try to delete
            Remove-Item "$file" -Force -ErrorAction Stop
            Write-Host "✅ Successfully deleted: $file"
        }
        catch {
            Write-Host "❌ Still failed to delete: $file"
            # Try using cmd delete
            try {
                cmd /c "del /f /q `"$file`"" 2>$null
                if (-not (Test-Path $file)) {
                    Write-Host "✅ Deleted with CMD: $file"
                }
            }
            catch {
                Write-Host "❌ CMD delete also failed: $file"
            }
        }
    }
}

Write-Host ""
Write-Host "Checking remaining files..."
$remainingFiles = 0
foreach ($file in $securityFiles) {
    if (Test-Path $file) {
        Write-Host "❌ Still exists: $file"
        $remainingFiles++
    }
}

if ($remainingFiles -eq 0) {
    Write-Host "🎉 All security files successfully deleted!"
    Write-Host "You can now create your ZIP file without access errors!"
} else {
    Write-Host "⚠️  $remainingFiles files still remain. You may need to:"
    Write-Host "   1. Close any running CircleUtility processes"
    Write-Host "   2. Run as Administrator"
    Write-Host "   3. Disable antivirus temporarily"
}
