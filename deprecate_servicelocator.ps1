# Add deprecation notice to ServiceLocator
$filePath = "CircleUtility\Services\ServiceLocator.cs"
$content = Get-Content $filePath -Raw

# Add deprecation notice and Obsolete attribute
$content = $content -replace '    /// <summary>
    /// Simple service locator to help break circular dependencies
    /// </summary>
    public static class ServiceLocator', '    /// <summary>
    /// Simple service locator to help break circular dependencies
    /// 
    /// ⚠️ DEPRECATED: This class implements the ServiceLocator anti-pattern.
    /// Use proper dependency injection through IServiceProvider instead.
    /// This class will be removed in a future version.
    /// </summary>
    [System.Obsolete("ServiceLocator is deprecated. Use proper dependency injection through IServiceProvider instead.", false)]
    public static class ServiceLocator'

Set-Content $filePath -Value $content

Write-Host "ServiceLocator marked as deprecated with Obsolete attribute!"
