@echo off
title The Circle Utility Debug Launcher
color 0A

echo.
echo  =======================================
echo  =                                     =
echo  =        THE CIRCLE UTILITY           =
echo  =                                     =
echo  =          DEBUG MODE                 =
echo  =                                     =
echo  =======================================
echo.
echo  Launching The Circle Utility in Debug Mode...
echo.

:: Create debug directory
set DEBUG_DIR=debug_output
if not exist %DEBUG_DIR% mkdir %DEBUG_DIR%

:: Set timestamp for log files
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"
set "LOG_FILE=%DEBUG_DIR%\debug_%TIMESTAMP%.log"

echo Debug session started at %TIMESTAMP% > %LOG_FILE%
echo. >> %LOG_FILE%

:: Check if .NET 6.0 is installed
echo Checking .NET installation... >> %LOG_FILE%
if exist "C:\Program Files\dotnet\dotnet.exe" (
    "C:\Program Files\dotnet\dotnet.exe" --list-runtimes >> %LOG_FILE% 2>&1
    echo. >> %LOG_FILE%
) else (
    echo .NET SDK is not installed. >> %LOG_FILE%
    echo Please install it from https://dotnet.microsoft.com/download >> %LOG_FILE%
    echo. >> %LOG_FILE%
    echo .NET SDK is not installed.
    echo Please install it from https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

:: Check if the executable exists
echo Checking executable... >> %LOG_FILE%
if not exist "CircleUtility\bin\Debug\net6.0-windows\CircleUtility.exe" (
    echo ERROR: Application executable not found. >> %LOG_FILE%
    echo Please build the application first using: >> %LOG_FILE%
    echo dotnet build CircleUtility.sln >> %LOG_FILE%
    echo. >> %LOG_FILE%
    
    echo ERROR: Application executable not found.
    echo Please build the application first using:
    echo dotnet build CircleUtility.sln
    echo.
    pause
    exit /b 1
)

:: Check if the DLL exists
echo Checking DLL... >> %LOG_FILE%
if not exist "CircleUtility\bin\Debug\net6.0-windows\CircleUtility.dll" (
    echo ERROR: Application DLL not found. >> %LOG_FILE%
    echo Please build the application first using: >> %LOG_FILE%
    echo dotnet build CircleUtility.sln >> %LOG_FILE%
    echo. >> %LOG_FILE%
    
    echo ERROR: Application DLL not found.
    echo Please build the application first using:
    echo dotnet build CircleUtility.sln
    echo.
    pause
    exit /b 1
)

:: List all files in the bin directory
echo Listing files in bin directory... >> %LOG_FILE%
dir "CircleUtility\bin\Debug\net6.0-windows" >> %LOG_FILE% 2>&1
echo. >> %LOG_FILE%

:: List all DLLs in the bin directory
echo Listing DLLs in bin directory... >> %LOG_FILE%
dir "CircleUtility\bin\Debug\net6.0-windows\*.dll" >> %LOG_FILE% 2>&1
echo. >> %LOG_FILE%

:: Try to run the application with dotnet exec
echo Running application with dotnet exec... >> %LOG_FILE%
echo Command: "C:\Program Files\dotnet\dotnet.exe" exec "CircleUtility\bin\Debug\net6.0-windows\CircleUtility.dll" >> %LOG_FILE%
echo. >> %LOG_FILE%
echo Running application with dotnet exec...
echo.
"C:\Program Files\dotnet\dotnet.exe" exec "CircleUtility\bin\Debug\net6.0-windows\CircleUtility.dll" >> %LOG_FILE% 2>&1
set DOTNET_EXEC_ERROR=%ERRORLEVEL%
echo. >> %LOG_FILE%
echo dotnet exec exited with code %DOTNET_EXEC_ERROR% >> %LOG_FILE%
echo. >> %LOG_FILE%

:: Try to run the application directly
echo Running application directly... >> %LOG_FILE%
echo Command: "CircleUtility\bin\Debug\net6.0-windows\CircleUtility.exe" >> %LOG_FILE%
echo. >> %LOG_FILE%
echo Running application directly...
echo.
"CircleUtility\bin\Debug\net6.0-windows\CircleUtility.exe" >> %LOG_FILE% 2>&1
set EXE_ERROR=%ERRORLEVEL%
echo. >> %LOG_FILE%
echo CircleUtility.exe exited with code %EXE_ERROR% >> %LOG_FILE%
echo. >> %LOG_FILE%

:: Check log files
echo Checking log files... >> %LOG_FILE%
echo. >> %LOG_FILE%

:: Find the most recent log file
for /f "tokens=*" %%a in ('dir /b /od "CircleUtility\bin\Debug\net6.0-windows\logs\AppStartup_*.log"') do set "LATEST_LOG=%%a"
echo Latest log file: %LATEST_LOG% >> %LOG_FILE%
echo. >> %LOG_FILE%

if exist "CircleUtility\bin\Debug\net6.0-windows\logs\%LATEST_LOG%" (
    echo Contents of latest log file: >> %LOG_FILE%
    type "CircleUtility\bin\Debug\net6.0-windows\logs\%LATEST_LOG%" >> %LOG_FILE%
    echo. >> %LOG_FILE%
) else (
    echo No log files found. >> %LOG_FILE%
    echo. >> %LOG_FILE%
)

echo Debug session completed at %TIME% >> %LOG_FILE%
echo.
echo Debug session completed. See %LOG_FILE% for details.
echo.
pause
exit /b 0
