# Contributing to Circle Utility

Thank you for your interest in contributing to Circle Utility! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [How to Contribute](#how-to-contribute)
   - [Reporting Bugs](#reporting-bugs)
   - [Suggesting Enhancements](#suggesting-enhancements)
   - [Code Contributions](#code-contributions)
4. [Development Workflow](#development-workflow)
   - [Branching Strategy](#branching-strategy)
   - [Commit Messages](#commit-messages)
   - [Pull Requests](#pull-requests)
5. [Coding Standards](#coding-standards)
6. [Testing](#testing)
7. [Documentation](#documentation)
8. [Community](#community)

## Code of Conduct

This project and everyone participating in it is governed by the [Circle Utility Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code. Please report unacceptable behavior to [<EMAIL>](mailto:<EMAIL>).

## Getting Started

### Prerequisites

- Visual Studio 2022 (Community, Professional, or Enterprise)
- .NET 6.0 SDK
- Windows 10/11 SDK
- Git for version control

### Setting Up the Development Environment

1. **Fork the Repository**:
   - Fork the repository on GitHub
   - Clone your fork locally:
     ```
     git clone https://github.com/YOUR-USERNAME/CircleUtility.git
     cd CircleUtility
     ```

2. **Add Upstream Remote**:
   ```
   git remote add upstream https://github.com/CircleUtility/CircleUtility.git
   ```

3. **Open the Solution**:
   - Open Visual Studio 2022
   - Open the `CircleUtility.sln` solution file

4. **Restore NuGet Packages**:
   - Right-click on the solution in Solution Explorer
   - Select "Restore NuGet Packages"

5. **Build the Solution**:
   - Build the solution by pressing F6 or selecting Build > Build Solution

6. **Run the Application**:
   - Press F5 or select Debug > Start Debugging to run the application

## How to Contribute

### Reporting Bugs

This section guides you through submitting a bug report. Following these guidelines helps maintainers understand your report, reproduce the issue, and find related reports.

**Before Submitting a Bug Report**:
- Check the [issue tracker](https://github.com/CircleUtility/CircleUtility/issues) to see if the problem has already been reported
- Ensure you're using the latest version of Circle Utility
- Collect information about your system (OS version, hardware, etc.)

**How to Submit a Bug Report**:
1. Go to the [issue tracker](https://github.com/CircleUtility/CircleUtility/issues)
2. Click "New Issue"
3. Select "Bug Report"
4. Fill out the template with all required information
5. Submit the issue

### Suggesting Enhancements

This section guides you through submitting an enhancement suggestion, including completely new features and minor improvements to existing functionality.

**Before Submitting an Enhancement Suggestion**:
- Check the [issue tracker](https://github.com/CircleUtility/CircleUtility/issues) to see if the enhancement has already been suggested
- Check the roadmap to see if the feature is already planned

**How to Submit an Enhancement Suggestion**:
1. Go to the [issue tracker](https://github.com/CircleUtility/CircleUtility/issues)
2. Click "New Issue"
3. Select "Feature Request"
4. Fill out the template with all required information
5. Submit the issue

### Code Contributions

#### Small Fixes

For small fixes (typos, minor bugs, etc.):

1. Create a branch in your fork
2. Make your changes
3. Submit a pull request

#### Larger Contributions

For larger contributions:

1. First, open an issue describing the contribution you're planning to make
2. Discuss the approach with the maintainers
3. Create a branch in your fork
4. Implement your changes
5. Submit a pull request

## Development Workflow

### Branching Strategy

- `main`: The main branch contains the latest stable release
- `develop`: The development branch contains the latest development changes
- Feature branches: Create from `develop` for new features or enhancements
- Bugfix branches: Create from `develop` for bug fixes
- Hotfix branches: Create from `main` for critical fixes to production

#### Branch Naming Convention

- Feature branches: `feature/short-description`
- Bugfix branches: `bugfix/issue-number-short-description`
- Hotfix branches: `hotfix/issue-number-short-description`

### Commit Messages

Write clear, concise commit messages that explain the changes made:

- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests after the first line

Example:
```
Add hardware detection for AMD GPUs

- Implement detection for Radeon RX series
- Add support for driver version detection
- Create UI indicators for AMD hardware

Fixes #123
```

### Pull Requests

1. **Create a Pull Request**:
   - Go to your fork on GitHub
   - Select your branch
   - Click "Pull Request"
   - Fill out the template

2. **PR Description**:
   - Clearly describe the changes
   - Reference any related issues
   - Include screenshots or GIFs for UI changes
   - List any dependencies or breaking changes

3. **Code Review**:
   - All PRs require at least one review from a maintainer
   - Address any feedback from reviewers
   - Make requested changes in the same branch

4. **Continuous Integration**:
   - All tests must pass
   - Code must meet quality standards
   - Documentation must be updated

5. **Merging**:
   - PRs are merged by maintainers
   - PRs should be rebased on the target branch before merging
   - Squash commits if necessary

## Coding Standards

Circle Utility follows Microsoft's C# coding conventions with some project-specific guidelines. For detailed information, see the [Developer Guide](CircleUtility/Documentation/DEVELOPER_GUIDE.md#coding-standards).

Key points:
- Use consistent naming conventions
- Write clear, self-documenting code
- Add XML documentation comments to public members
- Follow MVVM pattern for UI code
- Use dependency injection for services
- Write unit tests for new functionality

## Testing

All code contributions should include appropriate tests:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components
- **UI Tests**: Test user interface functionality

Run tests before submitting a PR:
```
dotnet test
```

## Documentation

Update documentation for any changes you make:

- **Code Comments**: Add or update XML documentation comments
- **README**: Update if your changes affect installation or usage
- **Developer Guide**: Update for architectural changes
- **User Guide**: Update for user-facing changes

## Community

- **Discord**: Join our [Discord server](https://discord.gg/circleutility) for discussions
- **GitHub Discussions**: Use [GitHub Discussions](https://github.com/CircleUtility/CircleUtility/discussions) for questions and ideas
- **Stack Overflow**: Tag questions with `circle-utility`

Thank you for contributing to Circle Utility!
