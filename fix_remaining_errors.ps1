# Fix the remaining 4 errors

Write-Host "1. Fixing LogEntryModel namespace issue..."
$logEntryModelPath = "CircleUtility\Models\LogEntryModel.cs"
$logEntryModelContent = Get-Content $logEntryModelPath -Raw
$logEntryModelContent = $logEntryModelContent -replace 'CircleUtility\.Services\.LogEntry', 'CircleUtility.Models.LogEntry'
Set-Content $logEntryModelPath -Value $logEntryModelContent

Write-Host "2. Fixing HardwareCompatibilityService IHardwareInfoProvider reference..."
$hardwareCompatPath = "CircleUtility\Services\HardwareCompatibilityService.cs"
$hardwareCompatContent = Get-Content $hardwareCompatPath -Raw
$hardwareCompatContent = $hardwareCompatContent -replace 'private readonly IHardwareInfoProvider', 'private readonly IHardwareDetectionService'
Set-Content $hardwareCompatPath -Value $hardwareCompatContent

Write-Host "3. Removing duplicate HardwareRecommendationService constructor..."
$hardwareRecPath = "CircleUtility\Services\HardwareRecommendationService.cs"
$hardwareRecContent = Get-Content $hardwareRecPath -Raw
# Remove lines that contain the duplicate constructor
$lines = $hardwareRecContent -split "`r?`n"
$cleanLines = @()
$skipNext = $false
foreach ($line in $lines) {
    if ($line -match 'internal HardwareRecommendationService\(' -or $skipNext) {
        $skipNext = $true
        if ($line -match '^\s*\}\s*$') {
            $skipNext = $false
        }
        continue
    }
    $cleanLines += $line
}
$hardwareRecContent = $cleanLines -join "`r`n"
Set-Content $hardwareRecPath -Value $hardwareRecContent

Write-Host "4. Fixing PerformanceMonitoringService return type..."
$perfPath = "CircleUtility\Services\PerformanceMonitoringService.cs"
$perfContent = Get-Content $perfPath -Raw
$perfContent = $perfContent -replace 'public object GetCurrentMetrics\(\)', 'public PerformanceMetrics GetCurrentMetrics()'
$perfContent = $perfContent -replace 'return new \{ CpuUsage = 0\.0, MemoryUsage = 0\.0 \};', 'return new PerformanceMetrics { CpuUsage = 0.0f, MemoryUsage = 0.0f };'
Set-Content $perfPath -Value $perfContent

Write-Host "All remaining errors fixed!"
